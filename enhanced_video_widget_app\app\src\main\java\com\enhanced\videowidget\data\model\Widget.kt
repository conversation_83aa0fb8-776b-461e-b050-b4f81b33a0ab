package com.enhanced.videowidget.data.model

import android.graphics.Bitmap

/**
 * Enhanced Widget data class with pause/play functionality
 */
data class Widget(
    val id: Int,
    val name: String,
    val description: String,
    val firstFrame: Bitmap? = null,
    val framesDirectory: String? = null,
    val framerate: Float = 30.0f,
    val mediaType: String = "video",
    val totalFrames: Int = 0,
    val currentFrameIndex: Int = 0,
    val isPlaying: Boolean = true,
    val isPaused: Boolean = false,
    val videoPath: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val lastUpdated: Long = System.currentTimeMillis()
) {
    
    /**
     * Get the next frame index for animation
     */
    fun getNextFrameIndex(): Int {
        return if (totalFrames > 0) {
            (currentFrameIndex + 1) % totalFrames
        } else {
            0
        }
    }
    
    /**
     * Check if widget should be animating
     */
    fun shouldAnimate(): Bo<PERSON>an {
        return isPlaying && !isPaused && mediaType == "video" && totalFrames > 1
    }
    
    /**
     * Create a copy with updated playback state
     */
    fun withPlaybackState(playing: <PERSON><PERSON><PERSON>, paused: Boolean = false): Widget {
        return copy(
            isPlaying = playing,
            isPaused = paused,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * Create a copy with updated frame index
     */
    fun withFrameIndex(frameIndex: Int): Widget {
        return copy(
            currentFrameIndex = frameIndex,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * Toggle pause/play state
     */
    fun togglePlayPause(): Widget {
        return if (isPaused) {
            copy(isPaused = false, isPlaying = true, lastUpdated = System.currentTimeMillis())
        } else {
            copy(isPaused = true, isPlaying = false, lastUpdated = System.currentTimeMillis())
        }
    }
}
