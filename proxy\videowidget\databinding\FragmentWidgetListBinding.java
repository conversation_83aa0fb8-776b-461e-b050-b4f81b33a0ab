package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager.widget.ViewPager;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.tabs.TabLayout;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentWidgetListBinding implements ViewBinding {
    public final TextView headerText;
    public final View indicator;
    public final MaterialButton moreBtn;
    public final MaterialButton notificationBtn;
    public final MaterialButton removeAdsBtn;
    private final ConstraintLayout rootView;
    public final TabLayout tab;
    public final FrameLayout tabFrame;
    public final ViewPager viewPager;

    private FragmentWidgetListBinding(ConstraintLayout constraintLayout, TextView textView, View view, MaterialButton materialButton, MaterialButton materialButton2, MaterialButton materialButton3, TabLayout tabLayout, FrameLayout frameLayout, ViewPager viewPager) {
        this.rootView = constraintLayout;
        this.headerText = textView;
        this.indicator = view;
        this.moreBtn = materialButton;
        this.notificationBtn = materialButton2;
        this.removeAdsBtn = materialButton3;
        this.tab = tabLayout;
        this.tabFrame = frameLayout;
        this.viewPager = viewPager;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentWidgetListBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentWidgetListBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_widget_list, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentWidgetListBinding bind(View view) {
        View viewFindChildViewById;
        int i = R.id.headerText;
        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
        if (textView != null && (viewFindChildViewById = ViewBindings.findChildViewById(view, (i = R.id.indicator))) != null) {
            i = R.id.moreBtn;
            MaterialButton materialButton = (MaterialButton) ViewBindings.findChildViewById(view, i);
            if (materialButton != null) {
                i = R.id.notificationBtn;
                MaterialButton materialButton2 = (MaterialButton) ViewBindings.findChildViewById(view, i);
                if (materialButton2 != null) {
                    i = R.id.removeAdsBtn;
                    MaterialButton materialButton3 = (MaterialButton) ViewBindings.findChildViewById(view, i);
                    if (materialButton3 != null) {
                        i = R.id.tab;
                        TabLayout tabLayout = (TabLayout) ViewBindings.findChildViewById(view, i);
                        if (tabLayout != null) {
                            i = R.id.tabFrame;
                            FrameLayout frameLayout = (FrameLayout) ViewBindings.findChildViewById(view, i);
                            if (frameLayout != null) {
                                i = R.id.viewPager;
                                ViewPager viewPager = (ViewPager) ViewBindings.findChildViewById(view, i);
                                if (viewPager != null) {
                                    return new FragmentWidgetListBinding((ConstraintLayout) view, textView, viewFindChildViewById, materialButton, materialButton2, materialButton3, tabLayout, frameLayout, viewPager);
                                }
                            }
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
