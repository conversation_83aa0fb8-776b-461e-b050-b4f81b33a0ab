package com.proxy.videowidget.presentation.fragments;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import com.proxy.videowidget.R;
import kotlin.Metadata;

/* compiled from: ExploreFragment.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002¨\u0006\u0003"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/ExploreFragment;", "Landroidx/fragment/app/Fragment;", "()V", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class ExploreFragment extends Fragment {
    public ExploreFragment() {
        super(R.layout.fragment_explore);
    }
}
