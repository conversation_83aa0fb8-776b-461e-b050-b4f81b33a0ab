package com.proxy.videowidget.data.di;

import android.app.NotificationManager;
import android.content.Context;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class AppModule_ProvideNotificationManagerFactory implements Factory<NotificationManager> {
    private final Provider<Context> contextProvider;

    public AppModule_ProvideNotificationManagerFactory(Provider<Context> provider) {
        this.contextProvider = provider;
    }

    @Override // javax.inject.Provider
    public NotificationManager get() {
        return provideNotificationManager(this.contextProvider.get());
    }

    public static AppModule_ProvideNotificationManagerFactory create(Provider<Context> provider) {
        return new AppModule_ProvideNotificationManagerFactory(provider);
    }

    public static NotificationManager provideNotificationManager(Context context) {
        return (NotificationManager) Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideNotificationManager(context));
    }
}
