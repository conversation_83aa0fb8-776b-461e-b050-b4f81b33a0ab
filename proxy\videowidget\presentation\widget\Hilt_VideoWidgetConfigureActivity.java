package com.proxy.videowidget.presentation.widget;

import android.content.Context;
import androidx.activity.contextaware.OnContextAvailableListener;
import com.proxy.videowidget.presentation.base.BasePermissionsActivity;
import dagger.hilt.internal.GeneratedComponentManagerHolder;
import dagger.hilt.internal.UnsafeCasts;

/* loaded from: classes3.dex */
public abstract class Hilt_VideoWidgetConfigureActivity extends BasePermissionsActivity {
    private boolean injected = false;

    Hilt_VideoWidgetConfigureActivity() {
        _initHiltInternal();
    }

    private void _initHiltInternal() {
        addOnContextAvailableListener(new OnContextAvailableListener() { // from class: com.proxy.videowidget.presentation.widget.Hilt_VideoWidgetConfigureActivity.1
            @Override // androidx.activity.contextaware.OnContextAvailableListener
            public void onContextAvailable(Context context) {
                Hilt_VideoWidgetConfigureActivity.this.inject();
            }
        });
    }

    @Override // com.proxy.videowidget.presentation.base.Hilt_BaseActivity
    protected void inject() {
        if (this.injected) {
            return;
        }
        this.injected = true;
        ((VideoWidgetConfigureActivity_GeneratedInjector) ((GeneratedComponentManagerHolder) UnsafeCasts.unsafeCast(this)).generatedComponent()).injectVideoWidgetConfigureActivity((VideoWidgetConfigureActivity) UnsafeCasts.unsafeCast(this));
    }
}
