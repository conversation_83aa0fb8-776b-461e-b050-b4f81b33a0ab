package com.proxy.videowidget.presentation;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.widget.Toast;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelLazy;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.viewmodel.CreationExtras;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.proxy.videowidget.R;
import com.proxy.videowidget.databinding.ActivityMainBinding;
import com.proxy.videowidget.domain.service.WidgetService;
import com.proxy.videowidget.presentation.adapter.Notification;
import com.proxy.videowidget.presentation.fragments.AccountFragment;
import com.proxy.videowidget.presentation.fragments.InstructionFragment;
import com.proxy.videowidget.presentation.fragments.WelcomeFragment;
import com.proxy.videowidget.presentation.viewmodel.NotificationViewModel;
import com.proxy.videowidget.presentation.widget.Widget;
import com.proxy.videowidget.repository.WidgetRepository;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.List;
import kotlin.Lazy;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;

/* compiled from: MainActivity.kt */
@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u0000 !2\u00020\u0001:\u0001!B\u0005¢\u0006\u0002\u0010\u0002J\b\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u000f\u001a\u00020\u000eH\u0002J\b\u0010\u0010\u001a\u00020\u000eH\u0002J\b\u0010\u0011\u001a\u00020\u0012H\u0002J\b\u0010\u0013\u001a\u00020\u0012H\u0002J\b\u0010\u0014\u001a\u00020\u0012H\u0002J\b\u0010\u0015\u001a\u00020\u000eH\u0002J\u000e\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0017\u001a\u00020\u0018J\u0012\u0010\u0019\u001a\u00020\u000e2\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0014J\b\u0010\u001c\u001a\u00020\u000eH\u0014J\u0006\u0010\u001d\u001a\u00020\u000eJ\u0006\u0010\u001e\u001a\u00020\u000eJ\b\u0010\u001f\u001a\u00020\u000eH\u0002J\b\u0010 \u001a\u00020\u000eH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.¢\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002¢\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u000e\u0010\u000b\u001a\u00020\fX\u0082.¢\u0006\u0002\n\u0000¨\u0006\""}, d2 = {"Lcom/proxy/videowidget/presentation/MainActivity;", "Lcom/proxy/videowidget/presentation/base/BaseActivity;", "()V", "binding", "Lcom/proxy/videowidget/databinding/ActivityMainBinding;", "notificationViewModel", "Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "getNotificationViewModel", "()Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "notificationViewModel$delegate", "Lkotlin/Lazy;", "sharedPreferences", "Landroid/content/SharedPreferences;", "adjustSystemBarsAppearance", "", "checkBatteryOptimizationStatus", "createNotificationChannel", "isFirstLaunch", "", "isInstructionCompleted", "isWidgetCreated", "loadAppropriateFragment", "loadFragment", "fragment", "Landroidx/fragment/app/Fragment;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "onWidgetCreated", "recheckBatteryOptimizationStatus", "showInstructionalToast", "startWidgets", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@AndroidEntryPoint
/* loaded from: classes3.dex */
public final class MainActivity extends Hilt_MainActivity {
    public static final String CHANNEL_ID = "ForegroundServiceChannel";
    public static final String KEY_FIRST_LAUNCH = "firstLaunch";
    public static final String KEY_INSTRUCTION_COMPLETED = "instructionCompleted";
    public static final String KEY_WIDGET_CREATED = "widgetCreated";
    public static final String PREFS_NAME = "VideoWidgetPrefs";
    private ActivityMainBinding binding;

    /* renamed from: notificationViewModel$delegate, reason: from kotlin metadata */
    private final Lazy notificationViewModel;
    private SharedPreferences sharedPreferences;

    public MainActivity() {
        final MainActivity mainActivity = this;
        final Function0 function0 = null;
        this.notificationViewModel = new ViewModelLazy(Reflection.getOrCreateKotlinClass(NotificationViewModel.class), new Function0<ViewModelStore>() { // from class: com.proxy.videowidget.presentation.MainActivity$special$$inlined$viewModels$default$2
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelStore invoke() {
                return mainActivity.getViewModelStore();
            }
        }, new Function0<ViewModelProvider.Factory>() { // from class: com.proxy.videowidget.presentation.MainActivity$special$$inlined$viewModels$default$1
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelProvider.Factory invoke() {
                return mainActivity.getDefaultViewModelProviderFactory();
            }
        }, new Function0<CreationExtras>() { // from class: com.proxy.videowidget.presentation.MainActivity$special$$inlined$viewModels$default$3
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final CreationExtras invoke() {
                CreationExtras creationExtras;
                Function0 function02 = function0;
                return (function02 == null || (creationExtras = (CreationExtras) function02.invoke()) == null) ? mainActivity.getDefaultViewModelCreationExtras() : creationExtras;
            }
        });
    }

    private final NotificationViewModel getNotificationViewModel() {
        return (NotificationViewModel) this.notificationViewModel.getValue();
    }

    @Override // com.proxy.videowidget.presentation.base.BaseActivity, com.proxy.videowidget.presentation.base.Hilt_BaseActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityMainBinding activityMainBindingInflate = ActivityMainBinding.inflate(getLayoutInflater());
        Intrinsics.checkNotNullExpressionValue(activityMainBindingInflate, "inflate(...)");
        this.binding = activityMainBindingInflate;
        if (activityMainBindingInflate == null) {
            Intrinsics.throwUninitializedPropertyAccessException("binding");
            activityMainBindingInflate = null;
        }
        setContentView(activityMainBindingInflate.getRoot());
        SharedPreferences sharedPreferences = getSharedPreferences("VideoWidgetPrefs", 0);
        Intrinsics.checkNotNullExpressionValue(sharedPreferences, "getSharedPreferences(...)");
        this.sharedPreferences = sharedPreferences;
        MainActivity mainActivity = this;
        MobileAds.initialize(mainActivity, new OnInitializationCompleteListener() { // from class: com.proxy.videowidget.presentation.MainActivity$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.ads.initialization.OnInitializationCompleteListener
            public final void onInitializationComplete(InitializationStatus initializationStatus) {
                Intrinsics.checkNotNullParameter(initializationStatus, "initializationStatus");
            }
        });
        createNotificationChannel();
        checkBatteryOptimizationStatus();
        WidgetRepository.INSTANCE.initialize(mainActivity);
        startWidgets();
        setRequestedOrientation(1);
        if (savedInstanceState == null) {
            loadAppropriateFragment();
        }
        adjustSystemBarsAppearance();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() {
        super.onResume();
        recheckBatteryOptimizationStatus();
    }

    private final void loadAppropriateFragment() {
        if (isWidgetCreated()) {
            loadFragment(new AccountFragment());
            return;
        }
        if (isInstructionCompleted()) {
            loadFragment(new InstructionFragment());
        } else if (isFirstLaunch()) {
            loadFragment(new WelcomeFragment());
        } else {
            loadFragment(new WelcomeFragment());
        }
    }

    private final void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= 26) {
            ((NotificationManager) getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel(CHANNEL_ID, "Foreground Service Channel", 3));
        }
    }

    private final void checkBatteryOptimizationStatus() {
        String packageName = getPackageName();
        Object systemService = getSystemService("power");
        Intrinsics.checkNotNull(systemService, "null cannot be cast to non-null type android.os.PowerManager");
        if (!((PowerManager) systemService).isIgnoringBatteryOptimizations(packageName)) {
            getNotificationViewModel().clearNotifications();
            getNotificationViewModel().addNotification(new Notification("Disable battery optimization", "Disable battery optimization to prevent the video widgets from abruptly stopping."));
            if (isFirstLaunch()) {
                Intent intent = new Intent("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
                intent.setData(Uri.parse("package:" + packageName));
                startActivity(intent);
                return;
            }
            return;
        }
        getNotificationViewModel().clearNotifications();
    }

    public final void recheckBatteryOptimizationStatus() {
        checkBatteryOptimizationStatus();
    }

    private final void startWidgets() {
        WidgetRepository.INSTANCE.getWidgets().observe(this, new MainActivity$sam$androidx_lifecycle_Observer$0(new Function1<List<? extends Widget>, Unit>() { // from class: com.proxy.videowidget.presentation.MainActivity.startWidgets.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(List<? extends Widget> list) {
                invoke2((List<Widget>) list);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(List<Widget> list) {
                Intrinsics.checkNotNull(list);
                MainActivity mainActivity = MainActivity.this;
                for (Widget widget : list) {
                    if (!WidgetService.INSTANCE.isWidgetActive(widget.getId())) {
                        Intent intent = new Intent(mainActivity, (Class<?>) WidgetService.class);
                        intent.putExtra("appWidgetId", widget.getId());
                        intent.putExtra("framesDirectory", widget.getFramesDirectory());
                        intent.putExtra("mediaType", widget.getMediaType());
                        mainActivity.startService(intent);
                    }
                }
            }
        }));
    }

    private final boolean isFirstLaunch() {
        SharedPreferences sharedPreferences = this.sharedPreferences;
        SharedPreferences sharedPreferences2 = null;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        boolean z = sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, true);
        if (z) {
            SharedPreferences sharedPreferences3 = this.sharedPreferences;
            if (sharedPreferences3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            } else {
                sharedPreferences2 = sharedPreferences3;
            }
            sharedPreferences2.edit().putBoolean(KEY_FIRST_LAUNCH, false).apply();
        }
        return z;
    }

    private final boolean isInstructionCompleted() {
        SharedPreferences sharedPreferences = this.sharedPreferences;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        return sharedPreferences.getBoolean(KEY_INSTRUCTION_COMPLETED, false);
    }

    private final boolean isWidgetCreated() {
        SharedPreferences sharedPreferences = this.sharedPreferences;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        return sharedPreferences.getBoolean(KEY_WIDGET_CREATED, false);
    }

    public final void loadFragment(Fragment fragment) {
        Intrinsics.checkNotNullParameter(fragment, "fragment");
        getSupportFragmentManager().beginTransaction().replace(R.id.fragment_container, fragment).commit();
    }

    public final void onWidgetCreated() {
        SharedPreferences sharedPreferences = this.sharedPreferences;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        sharedPreferences.edit().putBoolean(KEY_WIDGET_CREATED, true).apply();
        loadFragment(new AccountFragment());
    }

    private final void showInstructionalToast() {
        Toast.makeText(this, "Installed apps →\nVideo Widget", 1).show();
    }

    private final void adjustSystemBarsAppearance() {
        if (Build.VERSION.SDK_INT >= 30) {
            getWindow().getInsetsController();
        } else {
            getWindow().getDecorView().setSystemUiVisibility(5894);
        }
    }
}
