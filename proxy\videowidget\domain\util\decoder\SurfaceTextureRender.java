package com.proxy.videowidget.domain.util.decoder;

import android.graphics.SurfaceTexture;
import android.opengl.GLES20;
import android.opengl.Matrix;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.work.Data;
import com.google.android.exoplayer2.upstream.CmcdConfiguration;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: SurfaceTextureRender.kt */
@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0014\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\u0018\u0000 (2\u00020\u0001:\u0001(B\u0005¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017J\u0018\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0017H\u0002J\u0018\u0010\u001b\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\u00172\u0006\u0010\u001d\u001a\u00020\u0017H\u0002J\u0016\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020\u0004J\u0018\u0010$\u001a\u00020\u00042\u0006\u0010%\u001a\u00020\u00042\u0006\u0010&\u001a\u00020\u0017H\u0002J\u0006\u0010'\u001a\u00020\u0015R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082D¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082D¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\tX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006)"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/SurfaceTextureRender;", "", "()V", "FLOAT_SIZE_BYTES", "", "TRIANGLE_VERTICES_DATA_POS_OFFSET", "TRIANGLE_VERTICES_DATA_STRIDE_BYTES", "TRIANGLE_VERTICES_DATA_UV_OFFSET", "mvpMatrix", "", "mvpMatrixHandle", "positionHandle", "program", "stMatrix", "stMatrixHandle", "textureHandle", "textureID", "triangleVertices", "Ljava/nio/FloatBuffer;", "triangleVerticesData", "checkGlError", "", "op", "", "checkLocation", "location", "label", "createProgram", "vertexSource", "fragmentSource", "drawFrame", CmcdConfiguration.KEY_STREAM_TYPE, "Landroid/graphics/SurfaceTexture;", "invert", "", "getTextureId", "loadShader", "shaderType", "source", "surfaceCreated", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class SurfaceTextureRender {
    private static final String FRAGMENT_SHADER = "\n            #extension GL_OES_EGL_image_external : require\n            precision mediump float;\n            varying vec2 vTextureCoord;\n            uniform samplerExternalOES sTexture;\n            void main() {\n                gl_FragColor = texture2D(sTexture, vTextureCoord);\n            }\n        ";
    private static final String TAG = "SurfaceTextureRender";
    private static final String VERTEX_SHADER = "\n            uniform mat4 uMVPMatrix;\n            uniform mat4 uSTMatrix;\n            attribute vec4 aPosition;\n            attribute vec4 aTextureCoord;\n            varying vec2 vTextureCoord;\n            void main() {\n                gl_Position = uMVPMatrix * aPosition;\n                vTextureCoord = (uSTMatrix * aTextureCoord).xy;\n            }\n        ";
    private final int TRIANGLE_VERTICES_DATA_POS_OFFSET;
    private final float[] mvpMatrix;
    private int mvpMatrixHandle;
    private int positionHandle;
    private int program;
    private final float[] stMatrix;
    private int stMatrixHandle;
    private int textureHandle;
    private int textureID;
    private final FloatBuffer triangleVertices;
    private final float[] triangleVerticesData;
    private final int FLOAT_SIZE_BYTES = 4;
    private final int TRIANGLE_VERTICES_DATA_STRIDE_BYTES = 4 * 5;
    private final int TRIANGLE_VERTICES_DATA_UV_OFFSET = 3;

    public SurfaceTextureRender() {
        float[] fArr = {-1.0f, -1.0f, 0.0f, 0.0f, 0.0f, 1.0f, -1.0f, 0.0f, 1.0f, 0.0f, -1.0f, 1.0f, 0.0f, 0.0f, 1.0f, 1.0f, 1.0f, 0.0f, 1.0f, 1.0f};
        this.triangleVerticesData = fArr;
        FloatBuffer floatBufferAsFloatBuffer = ByteBuffer.allocateDirect(fArr.length * 4).order(ByteOrder.nativeOrder()).asFloatBuffer();
        floatBufferAsFloatBuffer.put(fArr).position(0);
        Intrinsics.checkNotNullExpressionValue(floatBufferAsFloatBuffer, "apply(...)");
        this.triangleVertices = floatBufferAsFloatBuffer;
        this.mvpMatrix = new float[16];
        float[] fArr2 = new float[16];
        this.stMatrix = fArr2;
        this.textureID = -12345;
        Matrix.setIdentityM(fArr2, 0);
    }

    /* renamed from: getTextureId, reason: from getter */
    public final int getTextureID() {
        return this.textureID;
    }

    public final void drawFrame(SurfaceTexture st, boolean invert) {
        Intrinsics.checkNotNullParameter(st, "st");
        checkGlError("onDrawFrame start");
        st.getTransformMatrix(this.stMatrix);
        GLES20.glClear(16640);
        GLES20.glUseProgram(this.program);
        checkGlError("glUseProgram");
        GLES20.glActiveTexture(33984);
        GLES20.glBindTexture(36197, this.textureID);
        this.triangleVertices.position(this.TRIANGLE_VERTICES_DATA_POS_OFFSET);
        GLES20.glVertexAttribPointer(this.positionHandle, 3, 5126, false, this.TRIANGLE_VERTICES_DATA_STRIDE_BYTES, (Buffer) this.triangleVertices);
        checkGlError("glVertexAttribPointer position");
        GLES20.glEnableVertexAttribArray(this.positionHandle);
        checkGlError("glEnableVertexAttribArray positionHandle");
        this.triangleVertices.position(this.TRIANGLE_VERTICES_DATA_UV_OFFSET);
        GLES20.glVertexAttribPointer(this.textureHandle, 2, 5126, false, this.TRIANGLE_VERTICES_DATA_STRIDE_BYTES, (Buffer) this.triangleVertices);
        checkGlError("glVertexAttribPointer textureHandle");
        GLES20.glEnableVertexAttribArray(this.textureHandle);
        checkGlError("glEnableVertexAttribArray textureHandle");
        Matrix.setIdentityM(this.mvpMatrix, 0);
        if (invert) {
            Matrix.scaleM(this.mvpMatrix, 0, 1.0f, -1.0f, 1.0f);
        }
        GLES20.glUniformMatrix4fv(this.mvpMatrixHandle, 1, false, this.mvpMatrix, 0);
        GLES20.glUniformMatrix4fv(this.stMatrixHandle, 1, false, this.stMatrix, 0);
        GLES20.glDrawArrays(5, 0, 4);
        checkGlError("glDrawArrays");
        GLES20.glBindTexture(36197, 0);
    }

    public final void surfaceCreated() {
        int iCreateProgram = createProgram(VERTEX_SHADER, FRAGMENT_SHADER);
        this.program = iCreateProgram;
        if (iCreateProgram == 0) {
            throw new RuntimeException("Failed creating program");
        }
        int iGlGetAttribLocation = GLES20.glGetAttribLocation(iCreateProgram, "aPosition");
        this.positionHandle = iGlGetAttribLocation;
        checkLocation(iGlGetAttribLocation, "aPosition");
        int iGlGetAttribLocation2 = GLES20.glGetAttribLocation(this.program, "aTextureCoord");
        this.textureHandle = iGlGetAttribLocation2;
        checkLocation(iGlGetAttribLocation2, "aTextureCoord");
        int iGlGetUniformLocation = GLES20.glGetUniformLocation(this.program, "uMVPMatrix");
        this.mvpMatrixHandle = iGlGetUniformLocation;
        checkLocation(iGlGetUniformLocation, "uMVPMatrix");
        int iGlGetUniformLocation2 = GLES20.glGetUniformLocation(this.program, "uSTMatrix");
        this.stMatrixHandle = iGlGetUniformLocation2;
        checkLocation(iGlGetUniformLocation2, "uSTMatrix");
        int[] iArr = new int[1];
        GLES20.glGenTextures(1, iArr, 0);
        int i = iArr[0];
        this.textureID = i;
        GLES20.glBindTexture(36197, i);
        checkGlError("glBindTexture textureID");
        GLES20.glTexParameterf(36197, 10241, 9728.0f);
        GLES20.glTexParameterf(36197, Data.MAX_DATA_BYTES, 9729.0f);
        GLES20.glTexParameteri(36197, 10242, 33071);
        GLES20.glTexParameteri(36197, 10243, 33071);
        checkGlError("glTexParameter");
    }

    public final void checkGlError(String op) {
        Intrinsics.checkNotNullParameter(op, "op");
        int iGlGetError = GLES20.glGetError();
        if (iGlGetError == 0) {
            return;
        }
        Log.e(TAG, op + ": glError " + iGlGetError);
        throw new RuntimeException(op + ": glError " + iGlGetError);
    }

    private final int loadShader(int shaderType, String source) {
        int iGlCreateShader = GLES20.glCreateShader(shaderType);
        checkGlError("glCreateShader type=" + shaderType);
        GLES20.glShaderSource(iGlCreateShader, source);
        GLES20.glCompileShader(iGlCreateShader);
        int[] iArr = new int[1];
        GLES20.glGetShaderiv(iGlCreateShader, 35713, iArr, 0);
        if (iArr[0] != 0) {
            return iGlCreateShader;
        }
        Log.e(TAG, "Could not compile shader " + shaderType + ':');
        Log.e(TAG, GLES20.glGetShaderInfoLog(iGlCreateShader));
        GLES20.glDeleteShader(iGlCreateShader);
        throw new RuntimeException("Could not compile shader " + shaderType);
    }

    private final int createProgram(String vertexSource, String fragmentSource) {
        int iLoadShader = loadShader(35633, vertexSource);
        int iLoadShader2 = loadShader(35632, fragmentSource);
        int iGlCreateProgram = GLES20.glCreateProgram();
        GLES20.glAttachShader(iGlCreateProgram, iLoadShader);
        GLES20.glAttachShader(iGlCreateProgram, iLoadShader2);
        GLES20.glLinkProgram(iGlCreateProgram);
        int[] iArr = new int[1];
        GLES20.glGetProgramiv(iGlCreateProgram, 35714, iArr, 0);
        if (iArr[0] == 1) {
            return iGlCreateProgram;
        }
        Log.e(TAG, "Could not link program: ");
        Log.e(TAG, GLES20.glGetProgramInfoLog(iGlCreateProgram));
        GLES20.glDeleteProgram(iGlCreateProgram);
        throw new RuntimeException("Could not link program");
    }

    private final void checkLocation(int location, String label) {
        if (location < 0) {
            throw new RuntimeException("Unable to locate '" + label + "' in program");
        }
    }
}
