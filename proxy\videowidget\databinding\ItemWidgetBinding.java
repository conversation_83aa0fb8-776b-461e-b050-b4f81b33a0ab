package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.exoplayer2.ui.PlayerView;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class ItemWidgetBinding implements ViewBinding {
    public final ImageView imageViewWidget;
    public final CardView imageViewWidgetCard;
    public final PlayerView playerView;
    private final ConstraintLayout rootView;

    private ItemWidgetBinding(ConstraintLayout constraintLayout, ImageView imageView, CardView cardView, PlayerView playerView) {
        this.rootView = constraintLayout;
        this.imageViewWidget = imageView;
        this.imageViewWidgetCard = cardView;
        this.playerView = playerView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ItemWidgetBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static ItemWidgetBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.item_widget, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static ItemWidgetBinding bind(View view) {
        int i = R.id.imageViewWidget;
        ImageView imageView = (ImageView) ViewBindings.findChildViewById(view, i);
        if (imageView != null) {
            i = R.id.imageViewWidgetCard;
            CardView cardView = (CardView) ViewBindings.findChildViewById(view, i);
            if (cardView != null) {
                i = R.id.playerView;
                PlayerView playerView = (PlayerView) ViewBindings.findChildViewById(view, i);
                if (playerView != null) {
                    return new ItemWidgetBinding((ConstraintLayout) view, imageView, cardView, playerView);
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
