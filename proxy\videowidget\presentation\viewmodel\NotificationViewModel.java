package com.proxy.videowidget.presentation.viewmodel;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import com.proxy.videowidget.presentation.adapter.Notification;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: NotificationViewModel.kt */
@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u0006J\u0006\u0010\u000e\u001a\u00020\fR\u001a\u0010\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004¢\u0006\u0002\n\u0000R\u001d\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\b8F¢\u0006\u0006\u001a\u0004\b\t\u0010\n¨\u0006\u000f"}, d2 = {"Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "Landroidx/lifecycle/ViewModel;", "()V", "_notifications", "Landroidx/lifecycle/MutableLiveData;", "", "Lcom/proxy/videowidget/presentation/adapter/Notification;", "notifications", "Landroidx/lifecycle/LiveData;", "getNotifications", "()Landroidx/lifecycle/LiveData;", "addNotification", "", "notification", "clearNotifications", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class NotificationViewModel extends ViewModel {
    private final MutableLiveData<List<Notification>> _notifications = new MutableLiveData<>(CollectionsKt.emptyList());

    public final LiveData<List<Notification>> getNotifications() {
        return this._notifications;
    }

    public final void addNotification(Notification notification) {
        Intrinsics.checkNotNullParameter(notification, "notification");
        List<Notification> value = this._notifications.getValue();
        if (value == null) {
            value = CollectionsKt.emptyList();
        }
        List<Notification> mutableList = CollectionsKt.toMutableList((Collection) value);
        mutableList.add(notification);
        this._notifications.setValue(mutableList);
    }

    public final void clearNotifications() {
        this._notifications.setValue(CollectionsKt.emptyList());
    }
}
