package com.proxy.videowidget.presentation.base;

import androidx.constraintlayout.widget.ConstraintLayout;
import dagger.hilt.android.HiltAndroidApp;
import kotlin.Metadata;

/* compiled from: BaseApp.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002¨\u0006\u0003"}, d2 = {"Lcom/proxy/videowidget/presentation/base/BaseApp;", "Landroid/app/Application;", "()V", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@HiltAndroidApp
/* loaded from: classes3.dex */
public final class BaseApp extends Hilt_BaseApp {
}
