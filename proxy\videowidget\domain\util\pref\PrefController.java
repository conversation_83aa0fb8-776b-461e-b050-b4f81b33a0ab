package com.proxy.videowidget.domain.util.pref;

import androidx.constraintlayout.widget.ConstraintLayout;
import javax.inject.Inject;
import javax.inject.Singleton;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: PrefController.kt */
@Singleton
@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\u0006\u0010\u0005\u001a\u00020\u0006J\u0006\u0010\u0007\u001a\u00020\u0006J\u0006\u0010\b\u001a\u00020\tJ\u0006\u0010\n\u001a\u00020\tJ\u0006\u0010\u000b\u001a\u00020\tJ\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\tJ\u000e\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\tJ\u000e\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\tJ\u000e\u0010\u0014\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0015"}, d2 = {"Lcom/proxy/videowidget/domain/util/pref/PrefController;", "", "myPref", "Lcom/proxy/videowidget/domain/util/pref/MyPref;", "(Lcom/proxy/videowidget/domain/util/pref/MyPref;)V", "clearWidgetData", "", "clearWidgetText", "getVideoPath", "", "getWidgetData", "getWidgetText", "isFirstTime", "", "saveVideoPath", "path", "saveWidgetData", "data", "saveWidgetText", "text", "setFirstTime", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class PrefController {
    private final MyPref myPref;

    @Inject
    public PrefController(MyPref myPref) {
        Intrinsics.checkNotNullParameter(myPref, "myPref");
        this.myPref = myPref;
    }

    public final void saveWidgetText(String text) {
        Intrinsics.checkNotNullParameter(text, "text");
        this.myPref.setWidgetText(text);
    }

    public final String getWidgetText() {
        return this.myPref.getWidgetText();
    }

    public final void clearWidgetText() {
        this.myPref.setWidgetText("");
    }

    public final void saveVideoPath(String path) {
        Intrinsics.checkNotNullParameter(path, "path");
        this.myPref.setVideoPath(path);
    }

    public final String getVideoPath() {
        return this.myPref.getVideoPath();
    }

    public final void setFirstTime(boolean isFirstTime) {
        this.myPref.setFirstTime(isFirstTime);
    }

    public final boolean isFirstTime() {
        return this.myPref.isFirstTime();
    }

    public final void saveWidgetData(String data) {
        Intrinsics.checkNotNullParameter(data, "data");
        this.myPref.setWidgetData(data);
    }

    public final String getWidgetData() {
        return this.myPref.getWidgetData();
    }

    public final void clearWidgetData() {
        this.myPref.setWidgetData("");
    }
}
