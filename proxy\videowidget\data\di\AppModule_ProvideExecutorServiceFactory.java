package com.proxy.videowidget.data.di;

import dagger.internal.Factory;
import dagger.internal.Preconditions;
import java.util.concurrent.ExecutorService;

/* loaded from: classes3.dex */
public final class AppModule_ProvideExecutorServiceFactory implements Factory<ExecutorService> {
    @Override // javax.inject.Provider
    public ExecutorService get() {
        return provideExecutorService();
    }

    public static AppModule_ProvideExecutorServiceFactory create() {
        return InstanceHolder.INSTANCE;
    }

    public static ExecutorService provideExecutorService() {
        return (ExecutorService) Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideExecutorService());
    }

    private static final class InstanceHolder {
        private static final AppModule_ProvideExecutorServiceFactory INSTANCE = new AppModule_ProvideExecutorServiceFactory();

        private InstanceHolder() {
        }
    }
}
