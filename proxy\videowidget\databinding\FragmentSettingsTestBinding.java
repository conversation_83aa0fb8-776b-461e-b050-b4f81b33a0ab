package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentSettingsTestBinding implements ViewBinding {
    public final MaterialButton backBtn;
    public final TextView headerText;
    public final ImageView languageIcon;
    public final MaterialCardView languageOptionCard;
    public final TextView languageText;
    public final ImageView legalIcon;
    public final MaterialCardView legalOptionCard;
    public final TextView legalText;
    public final LinearLayout optionsList;
    private final ConstraintLayout rootView;
    public final ImageView themeIcon;
    public final MaterialCardView themeOptionCard;
    public final TextView themeText;
    public final TextView versionInfo;

    private FragmentSettingsTestBinding(ConstraintLayout constraintLayout, MaterialButton materialButton, TextView textView, ImageView imageView, MaterialCardView materialCardView, TextView textView2, ImageView imageView2, MaterialCardView materialCardView2, TextView textView3, LinearLayout linearLayout, ImageView imageView3, MaterialCardView materialCardView3, TextView textView4, TextView textView5) {
        this.rootView = constraintLayout;
        this.backBtn = materialButton;
        this.headerText = textView;
        this.languageIcon = imageView;
        this.languageOptionCard = materialCardView;
        this.languageText = textView2;
        this.legalIcon = imageView2;
        this.legalOptionCard = materialCardView2;
        this.legalText = textView3;
        this.optionsList = linearLayout;
        this.themeIcon = imageView3;
        this.themeOptionCard = materialCardView3;
        this.themeText = textView4;
        this.versionInfo = textView5;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentSettingsTestBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentSettingsTestBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_settings_test, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentSettingsTestBinding bind(View view) {
        int i = R.id.backBtn;
        MaterialButton materialButton = (MaterialButton) ViewBindings.findChildViewById(view, i);
        if (materialButton != null) {
            i = R.id.headerText;
            TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
            if (textView != null) {
                i = R.id.languageIcon;
                ImageView imageView = (ImageView) ViewBindings.findChildViewById(view, i);
                if (imageView != null) {
                    i = R.id.languageOptionCard;
                    MaterialCardView materialCardView = (MaterialCardView) ViewBindings.findChildViewById(view, i);
                    if (materialCardView != null) {
                        i = R.id.languageText;
                        TextView textView2 = (TextView) ViewBindings.findChildViewById(view, i);
                        if (textView2 != null) {
                            i = R.id.legalIcon;
                            ImageView imageView2 = (ImageView) ViewBindings.findChildViewById(view, i);
                            if (imageView2 != null) {
                                i = R.id.legalOptionCard;
                                MaterialCardView materialCardView2 = (MaterialCardView) ViewBindings.findChildViewById(view, i);
                                if (materialCardView2 != null) {
                                    i = R.id.legalText;
                                    TextView textView3 = (TextView) ViewBindings.findChildViewById(view, i);
                                    if (textView3 != null) {
                                        i = R.id.optionsList;
                                        LinearLayout linearLayout = (LinearLayout) ViewBindings.findChildViewById(view, i);
                                        if (linearLayout != null) {
                                            i = R.id.themeIcon;
                                            ImageView imageView3 = (ImageView) ViewBindings.findChildViewById(view, i);
                                            if (imageView3 != null) {
                                                i = R.id.themeOptionCard;
                                                MaterialCardView materialCardView3 = (MaterialCardView) ViewBindings.findChildViewById(view, i);
                                                if (materialCardView3 != null) {
                                                    i = R.id.themeText;
                                                    TextView textView4 = (TextView) ViewBindings.findChildViewById(view, i);
                                                    if (textView4 != null) {
                                                        i = R.id.versionInfo;
                                                        TextView textView5 = (TextView) ViewBindings.findChildViewById(view, i);
                                                        if (textView5 != null) {
                                                            return new FragmentSettingsTestBinding((ConstraintLayout) view, materialButton, textView, imageView, materialCardView, textView2, imageView2, materialCardView2, textView3, linearLayout, imageView3, materialCardView3, textView4, textView5);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
