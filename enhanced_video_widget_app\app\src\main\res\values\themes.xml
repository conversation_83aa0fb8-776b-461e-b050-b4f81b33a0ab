<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.EnhancedVideoWidget" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.EnhancedVideoWidget.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.EnhancedVideoWidget.AppWidgetContainer" parent="android:Theme">
        <!-- Radius of the outer bound of widgets to make the rounded corners -->
        <item name="appWidgetRadius">16dp</item>
        <!-- Radius of the inner view's bound of widgets to make the rounded corners -->
        <item name="appWidgetInnerRadius">8dp</item>
    </style>

    <style name="Theme.EnhancedVideoWidget.AppWidgetContainerParent" parent="@android:style/Theme.DeviceDefault">
        <!-- Radius of the outer bound of widgets to make the rounded corners -->
        <item name="appWidgetRadius">16dp</item>
        <!-- Radius of the inner view's bound of widgets to make the rounded corners -->
        <item name="appWidgetInnerRadius">8dp</item>
    </style>
</resources>
