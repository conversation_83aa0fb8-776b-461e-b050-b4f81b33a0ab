package com.proxy.videowidget.domain.util.pref;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.gms.measurement.api.AppMeasurementSdk;
import dagger.hilt.android.qualifiers.ApplicationContext;
import javax.inject.Inject;
import javax.inject.Singleton;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: MyPref.kt */
@Singleton
@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R$\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u00068F@FX\u0086\u000e¢\u0006\f\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u001b\u0010\u000b\u001a\u00020\f8BX\u0082\u0084\u0002¢\u0006\f\n\u0004\b\u000f\u0010\u0010\u001a\u0004\b\r\u0010\u000eR$\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00118F@FX\u0086\u000e¢\u0006\f\u001a\u0004\b\u0013\u0010\u0014\"\u0004\b\u0015\u0010\u0016R$\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00118F@FX\u0086\u000e¢\u0006\f\u001a\u0004\b\u0018\u0010\u0014\"\u0004\b\u0019\u0010\u0016R$\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00118F@FX\u0086\u000e¢\u0006\f\u001a\u0004\b\u001b\u0010\u0014\"\u0004\b\u001c\u0010\u0016¨\u0006\u001d"}, d2 = {"Lcom/proxy/videowidget/domain/util/pref/MyPref;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", AppMeasurementSdk.ConditionalUserProperty.VALUE, "", "isFirstTime", "()Z", "setFirstTime", "(Z)V", "pref", "Landroid/content/SharedPreferences;", "getPref", "()Landroid/content/SharedPreferences;", "pref$delegate", "Lkotlin/Lazy;", "", "videoPath", "getVideoPath", "()Ljava/lang/String;", "setVideoPath", "(Ljava/lang/String;)V", "widgetData", "getWidgetData", "setWidgetData", "widgetText", "getWidgetText", "setWidgetText", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class MyPref {
    private final Context context;

    /* renamed from: pref$delegate, reason: from kotlin metadata */
    private final Lazy pref;

    @Inject
    public MyPref(@ApplicationContext Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        this.context = context;
        this.pref = LazyKt.lazy(new Function0<SharedPreferences>() { // from class: com.proxy.videowidget.domain.util.pref.MyPref$pref$2
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final SharedPreferences invoke() {
                return this.this$0.context.getSharedPreferences("Main", 0);
            }
        });
    }

    private final SharedPreferences getPref() {
        Object value = this.pref.getValue();
        Intrinsics.checkNotNullExpressionValue(value, "getValue(...)");
        return (SharedPreferences) value;
    }

    public final String getWidgetText() {
        String string = getPref().getString("widgetText", "");
        return string == null ? "" : string;
    }

    public final void setWidgetText(String value) {
        Intrinsics.checkNotNullParameter(value, "value");
        getPref().edit().putString("widgetText", value).apply();
    }

    public final String getVideoPath() {
        String string = getPref().getString("videoPath", "");
        return string == null ? "" : string;
    }

    public final void setVideoPath(String value) {
        Intrinsics.checkNotNullParameter(value, "value");
        getPref().edit().putString("videoPath", value).apply();
    }

    public final boolean isFirstTime() {
        return getPref().getBoolean("isFirstTime", true);
    }

    public final void setFirstTime(boolean z) {
        getPref().edit().putBoolean("isFirstTime", z).apply();
    }

    public final String getWidgetData() {
        String string = getPref().getString("widgetData", "");
        return string == null ? "" : string;
    }

    public final void setWidgetData(String value) {
        Intrinsics.checkNotNullParameter(value, "value");
        getPref().edit().putString("widgetData", value).apply();
    }
}
