package com.bumptech.glide;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static int abc_fade_in = 0x7f010000;
        public static int abc_fade_out = 0x7f010001;
        public static int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static int abc_popup_enter = 0x7f010003;
        public static int abc_popup_exit = 0x7f010004;
        public static int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static int abc_slide_in_bottom = 0x7f010006;
        public static int abc_slide_in_top = 0x7f010007;
        public static int abc_slide_out_bottom = 0x7f010008;
        public static int abc_slide_out_top = 0x7f010009;
        public static int abc_tooltip_enter = 0x7f01000a;
        public static int abc_tooltip_exit = 0x7f01000b;
        public static int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
        public static int fragment_fast_out_extra_slow_in = 0x7f01001c;

        private anim() {
        }
    }

    public static final class animator {
        public static int fragment_close_enter = 0x7f020003;
        public static int fragment_close_exit = 0x7f020004;
        public static int fragment_fade_enter = 0x7f020005;
        public static int fragment_fade_exit = 0x7f020006;
        public static int fragment_open_enter = 0x7f020007;
        public static int fragment_open_exit = 0x7f020008;

        private animator() {
        }
    }

    public static final class attr {
        public static int actionBarDivider = 0x7f040002;
        public static int actionBarItemBackground = 0x7f040003;
        public static int actionBarPopupTheme = 0x7f040004;
        public static int actionBarSize = 0x7f040005;
        public static int actionBarSplitStyle = 0x7f040006;
        public static int actionBarStyle = 0x7f040007;
        public static int actionBarTabBarStyle = 0x7f040008;
        public static int actionBarTabStyle = 0x7f040009;
        public static int actionBarTabTextStyle = 0x7f04000a;
        public static int actionBarTheme = 0x7f04000b;
        public static int actionBarWidgetTheme = 0x7f04000c;
        public static int actionButtonStyle = 0x7f04000d;
        public static int actionDropDownStyle = 0x7f04000e;
        public static int actionLayout = 0x7f04000f;
        public static int actionMenuTextAppearance = 0x7f040010;
        public static int actionMenuTextColor = 0x7f040011;
        public static int actionModeBackground = 0x7f040012;
        public static int actionModeCloseButtonStyle = 0x7f040013;
        public static int actionModeCloseContentDescription = 0x7f040014;
        public static int actionModeCloseDrawable = 0x7f040015;
        public static int actionModeCopyDrawable = 0x7f040016;
        public static int actionModeCutDrawable = 0x7f040017;
        public static int actionModeFindDrawable = 0x7f040018;
        public static int actionModePasteDrawable = 0x7f040019;
        public static int actionModePopupWindowStyle = 0x7f04001a;
        public static int actionModeSelectAllDrawable = 0x7f04001b;
        public static int actionModeShareDrawable = 0x7f04001c;
        public static int actionModeSplitBackground = 0x7f04001d;
        public static int actionModeStyle = 0x7f04001e;
        public static int actionModeTheme = 0x7f04001f;
        public static int actionModeWebSearchDrawable = 0x7f040020;
        public static int actionOverflowButtonStyle = 0x7f040021;
        public static int actionOverflowMenuStyle = 0x7f040022;
        public static int actionProviderClass = 0x7f040023;
        public static int actionViewClass = 0x7f040025;
        public static int activityChooserViewStyle = 0x7f040027;
        public static int alertDialogButtonGroupStyle = 0x7f04002e;
        public static int alertDialogCenterButtons = 0x7f04002f;
        public static int alertDialogStyle = 0x7f040030;
        public static int alertDialogTheme = 0x7f040031;
        public static int allowStacking = 0x7f040032;
        public static int alpha = 0x7f040033;
        public static int alphabeticModifiers = 0x7f040034;
        public static int arrowHeadLength = 0x7f040042;
        public static int arrowShaftLength = 0x7f040043;
        public static int autoCompleteTextViewStyle = 0x7f040048;
        public static int autoSizeMaxTextSize = 0x7f04004a;
        public static int autoSizeMinTextSize = 0x7f04004b;
        public static int autoSizePresetSizes = 0x7f04004c;
        public static int autoSizeStepGranularity = 0x7f04004d;
        public static int autoSizeTextType = 0x7f04004e;
        public static int background = 0x7f040052;
        public static int backgroundSplit = 0x7f040059;
        public static int backgroundStacked = 0x7f04005a;
        public static int backgroundTint = 0x7f04005b;
        public static int backgroundTintMode = 0x7f04005c;
        public static int barLength = 0x7f04006e;
        public static int borderlessButtonStyle = 0x7f040084;
        public static int buttonBarButtonStyle = 0x7f040098;
        public static int buttonBarNegativeButtonStyle = 0x7f040099;
        public static int buttonBarNeutralButtonStyle = 0x7f04009a;
        public static int buttonBarPositiveButtonStyle = 0x7f04009b;
        public static int buttonBarStyle = 0x7f04009c;
        public static int buttonCompat = 0x7f04009d;
        public static int buttonGravity = 0x7f04009e;
        public static int buttonIconDimen = 0x7f0400a0;
        public static int buttonPanelSideLayout = 0x7f0400a3;
        public static int buttonStyle = 0x7f0400a5;
        public static int buttonStyleSmall = 0x7f0400a6;
        public static int buttonTint = 0x7f0400a7;
        public static int buttonTintMode = 0x7f0400a8;
        public static int checkboxStyle = 0x7f0400c1;
        public static int checkedTextViewStyle = 0x7f0400cc;
        public static int closeIcon = 0x7f0400ef;
        public static int closeItemLayout = 0x7f0400f6;
        public static int collapseContentDescription = 0x7f0400f7;
        public static int collapseIcon = 0x7f0400f8;
        public static int color = 0x7f040102;
        public static int colorAccent = 0x7f040103;
        public static int colorBackgroundFloating = 0x7f040104;
        public static int colorButtonNormal = 0x7f040105;
        public static int colorControlActivated = 0x7f040107;
        public static int colorControlHighlight = 0x7f040108;
        public static int colorControlNormal = 0x7f040109;
        public static int colorError = 0x7f04010a;
        public static int colorPrimary = 0x7f040123;
        public static int colorPrimaryDark = 0x7f040125;
        public static int colorSwitchThumbNormal = 0x7f04013b;
        public static int commitIcon = 0x7f040140;
        public static int contentDescription = 0x7f04014a;
        public static int contentInsetEnd = 0x7f04014b;
        public static int contentInsetEndWithActions = 0x7f04014c;
        public static int contentInsetLeft = 0x7f04014d;
        public static int contentInsetRight = 0x7f04014e;
        public static int contentInsetStart = 0x7f04014f;
        public static int contentInsetStartWithNavigation = 0x7f040150;
        public static int controlBackground = 0x7f04015a;
        public static int customNavigationLayout = 0x7f04017a;
        public static int defaultQueryHint = 0x7f040184;
        public static int dialogCornerRadius = 0x7f04018b;
        public static int dialogPreferredPadding = 0x7f04018c;
        public static int dialogTheme = 0x7f04018d;
        public static int displayOptions = 0x7f04018e;
        public static int divider = 0x7f04018f;
        public static int dividerHorizontal = 0x7f040191;
        public static int dividerPadding = 0x7f040194;
        public static int dividerVertical = 0x7f040196;
        public static int drawableBottomCompat = 0x7f04019b;
        public static int drawableEndCompat = 0x7f04019c;
        public static int drawableLeftCompat = 0x7f04019d;
        public static int drawableRightCompat = 0x7f04019e;
        public static int drawableSize = 0x7f04019f;
        public static int drawableStartCompat = 0x7f0401a0;
        public static int drawableTint = 0x7f0401a1;
        public static int drawableTintMode = 0x7f0401a2;
        public static int drawableTopCompat = 0x7f0401a3;
        public static int drawerArrowStyle = 0x7f0401a4;
        public static int dropDownListViewStyle = 0x7f0401a8;
        public static int dropdownListPreferredItemHeight = 0x7f0401a9;
        public static int editTextBackground = 0x7f0401ac;
        public static int editTextColor = 0x7f0401ad;
        public static int editTextStyle = 0x7f0401ae;
        public static int elevation = 0x7f0401af;
        public static int expandActivityOverflowButtonDrawable = 0x7f0401ca;
        public static int firstBaselineToTopHeight = 0x7f0401eb;
        public static int font = 0x7f04020e;
        public static int fontFamily = 0x7f04020f;
        public static int fontProviderAuthority = 0x7f040210;
        public static int fontProviderCerts = 0x7f040211;
        public static int fontProviderFetchStrategy = 0x7f040212;
        public static int fontProviderFetchTimeout = 0x7f040213;
        public static int fontProviderPackage = 0x7f040214;
        public static int fontProviderQuery = 0x7f040215;
        public static int fontProviderSystemFontFamily = 0x7f040216;
        public static int fontStyle = 0x7f040217;
        public static int fontVariationSettings = 0x7f040218;
        public static int fontWeight = 0x7f040219;
        public static int gapBetweenBars = 0x7f04021f;
        public static int goIcon = 0x7f040222;
        public static int height = 0x7f040227;
        public static int hideOnContentScroll = 0x7f04022f;
        public static int homeAsUpIndicator = 0x7f040237;
        public static int homeLayout = 0x7f040238;
        public static int icon = 0x7f04023c;
        public static int iconTint = 0x7f040242;
        public static int iconTintMode = 0x7f040243;
        public static int iconifiedByDefault = 0x7f040244;
        public static int imageButtonStyle = 0x7f040249;
        public static int indeterminateProgressStyle = 0x7f04024f;
        public static int initialActivityCount = 0x7f040256;
        public static int isLightTheme = 0x7f040258;
        public static int itemPadding = 0x7f040267;
        public static int lastBaselineToBottomHeight = 0x7f040284;
        public static int layout = 0x7f040286;
        public static int lineHeight = 0x7f0402d3;
        public static int listChoiceBackgroundIndicator = 0x7f0402d6;
        public static int listChoiceIndicatorMultipleAnimated = 0x7f0402d7;
        public static int listChoiceIndicatorSingleAnimated = 0x7f0402d8;
        public static int listDividerAlertDialog = 0x7f0402d9;
        public static int listItemLayout = 0x7f0402da;
        public static int listLayout = 0x7f0402db;
        public static int listMenuViewStyle = 0x7f0402dc;
        public static int listPopupWindowStyle = 0x7f0402dd;
        public static int listPreferredItemHeight = 0x7f0402de;
        public static int listPreferredItemHeightLarge = 0x7f0402df;
        public static int listPreferredItemHeightSmall = 0x7f0402e0;
        public static int listPreferredItemPaddingEnd = 0x7f0402e1;
        public static int listPreferredItemPaddingLeft = 0x7f0402e2;
        public static int listPreferredItemPaddingRight = 0x7f0402e3;
        public static int listPreferredItemPaddingStart = 0x7f0402e4;
        public static int logo = 0x7f0402e5;
        public static int logoDescription = 0x7f0402e7;
        public static int maxButtonHeight = 0x7f040333;
        public static int measureWithLargestChild = 0x7f04033b;
        public static int menu = 0x7f04033c;
        public static int multiChoiceItemLayout = 0x7f040379;
        public static int navigationContentDescription = 0x7f04037a;
        public static int navigationIcon = 0x7f04037b;
        public static int navigationMode = 0x7f04037d;
        public static int numericModifiers = 0x7f040384;
        public static int overlapAnchor = 0x7f04038d;
        public static int paddingBottomNoButtons = 0x7f04038f;
        public static int paddingEnd = 0x7f040391;
        public static int paddingStart = 0x7f040394;
        public static int paddingTopNoTitle = 0x7f040396;
        public static int panelBackground = 0x7f040398;
        public static int panelMenuListTheme = 0x7f040399;
        public static int panelMenuListWidth = 0x7f04039a;
        public static int popupMenuStyle = 0x7f0403b1;
        public static int popupTheme = 0x7f0403b2;
        public static int popupWindowStyle = 0x7f0403b3;
        public static int preserveIconSpacing = 0x7f0403b7;
        public static int progressBarPadding = 0x7f0403b9;
        public static int progressBarStyle = 0x7f0403ba;
        public static int queryBackground = 0x7f0403be;
        public static int queryHint = 0x7f0403bf;
        public static int radioButtonStyle = 0x7f0403c1;
        public static int ratingBarStyle = 0x7f0403c3;
        public static int ratingBarStyleIndicator = 0x7f0403c4;
        public static int ratingBarStyleSmall = 0x7f0403c5;
        public static int searchHintIcon = 0x7f0403e2;
        public static int searchIcon = 0x7f0403e3;
        public static int searchViewStyle = 0x7f0403e5;
        public static int seekBarStyle = 0x7f0403e6;
        public static int selectableItemBackground = 0x7f0403e7;
        public static int selectableItemBackgroundBorderless = 0x7f0403e8;
        public static int showAsAction = 0x7f04040e;
        public static int showDividers = 0x7f040410;
        public static int showText = 0x7f040414;
        public static int showTitle = 0x7f040415;
        public static int singleChoiceItemLayout = 0x7f040427;
        public static int spinBars = 0x7f040430;
        public static int spinnerDropDownItemStyle = 0x7f040431;
        public static int spinnerStyle = 0x7f040432;
        public static int splitTrack = 0x7f040433;
        public static int srcCompat = 0x7f040439;
        public static int state_above_anchor = 0x7f040443;
        public static int subMenuArrow = 0x7f040451;
        public static int submitBackground = 0x7f040456;
        public static int subtitle = 0x7f040457;
        public static int subtitleTextAppearance = 0x7f040459;
        public static int subtitleTextColor = 0x7f04045a;
        public static int subtitleTextStyle = 0x7f04045b;
        public static int suggestionRowLayout = 0x7f04045f;
        public static int switchMinWidth = 0x7f040461;
        public static int switchPadding = 0x7f040462;
        public static int switchStyle = 0x7f040463;
        public static int switchTextAppearance = 0x7f040464;
        public static int textAllCaps = 0x7f040486;
        public static int textAppearanceLargePopupMenu = 0x7f04049d;
        public static int textAppearanceListItem = 0x7f04049f;
        public static int textAppearanceListItemSecondary = 0x7f0404a0;
        public static int textAppearanceListItemSmall = 0x7f0404a1;
        public static int textAppearancePopupMenuHeader = 0x7f0404a3;
        public static int textAppearanceSearchResultSubtitle = 0x7f0404a4;
        public static int textAppearanceSearchResultTitle = 0x7f0404a5;
        public static int textAppearanceSmallPopupMenu = 0x7f0404a6;
        public static int textColorAlertDialogListItem = 0x7f0404b1;
        public static int textColorSearchUrl = 0x7f0404b2;
        public static int textLocale = 0x7f0404bd;
        public static int theme = 0x7f0404c7;
        public static int thickness = 0x7f0404c8;
        public static int thumbTextPadding = 0x7f0404d3;
        public static int thumbTint = 0x7f0404d4;
        public static int thumbTintMode = 0x7f0404d5;
        public static int tickMark = 0x7f0404db;
        public static int tickMarkTint = 0x7f0404dc;
        public static int tickMarkTintMode = 0x7f0404dd;
        public static int tint = 0x7f0404e2;
        public static int tintMode = 0x7f0404e3;
        public static int title = 0x7f0404e5;
        public static int titleMargin = 0x7f0404e9;
        public static int titleMarginBottom = 0x7f0404ea;
        public static int titleMarginEnd = 0x7f0404eb;
        public static int titleMarginStart = 0x7f0404ec;
        public static int titleMarginTop = 0x7f0404ed;
        public static int titleMargins = 0x7f0404ee;
        public static int titleTextAppearance = 0x7f0404f0;
        public static int titleTextColor = 0x7f0404f1;
        public static int titleTextStyle = 0x7f0404f3;
        public static int toolbarNavigationButtonStyle = 0x7f0404f6;
        public static int toolbarStyle = 0x7f0404f7;
        public static int tooltipForegroundColor = 0x7f0404f9;
        public static int tooltipFrameBackground = 0x7f0404fa;
        public static int tooltipText = 0x7f0404fc;
        public static int track = 0x7f040502;
        public static int trackTint = 0x7f04050e;
        public static int trackTintMode = 0x7f04050f;
        public static int ttcIndex = 0x7f040519;
        public static int viewInflaterClass = 0x7f040524;
        public static int voiceIcon = 0x7f04052a;
        public static int windowActionBar = 0x7f040532;
        public static int windowActionBarOverlay = 0x7f040533;
        public static int windowActionModeOverlay = 0x7f040534;
        public static int windowFixedHeightMajor = 0x7f040535;
        public static int windowFixedHeightMinor = 0x7f040536;
        public static int windowFixedWidthMajor = 0x7f040537;
        public static int windowFixedWidthMinor = 0x7f040538;
        public static int windowMinWidthMajor = 0x7f040539;
        public static int windowMinWidthMinor = 0x7f04053a;
        public static int windowNoTitle = 0x7f04053b;

        private attr() {
        }
    }

    public static final class bool {
        public static int abc_action_bar_embed_tabs = 0x7f050000;
        public static int abc_config_actionMenuItemAllCaps = 0x7f050001;

        private bool() {
        }
    }

    public static final class color {
        public static int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static int abc_btn_colored_text_material = 0x7f060003;
        public static int abc_color_highlight_material = 0x7f060004;
        public static int abc_decor_view_status_guard = 0x7f060005;
        public static int abc_decor_view_status_guard_light = 0x7f060006;
        public static int abc_hint_foreground_material_dark = 0x7f060007;
        public static int abc_hint_foreground_material_light = 0x7f060008;
        public static int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static int abc_primary_text_material_dark = 0x7f06000b;
        public static int abc_primary_text_material_light = 0x7f06000c;
        public static int abc_search_url_text = 0x7f06000d;
        public static int abc_search_url_text_normal = 0x7f06000e;
        public static int abc_search_url_text_pressed = 0x7f06000f;
        public static int abc_search_url_text_selected = 0x7f060010;
        public static int abc_secondary_text_material_dark = 0x7f060011;
        public static int abc_secondary_text_material_light = 0x7f060012;
        public static int abc_tint_btn_checkable = 0x7f060013;
        public static int abc_tint_default = 0x7f060014;
        public static int abc_tint_edittext = 0x7f060015;
        public static int abc_tint_seek_thumb = 0x7f060016;
        public static int abc_tint_spinner = 0x7f060017;
        public static int abc_tint_switch_track = 0x7f060018;
        public static int accent_material_dark = 0x7f060019;
        public static int accent_material_light = 0x7f06001a;
        public static int androidx_core_ripple_material_light = 0x7f06001b;
        public static int androidx_core_secondary_text_default_material_light = 0x7f06001c;
        public static int background_floating_material_dark = 0x7f06001f;
        public static int background_floating_material_light = 0x7f060020;
        public static int background_material_dark = 0x7f060021;
        public static int background_material_light = 0x7f060022;
        public static int bright_foreground_disabled_material_dark = 0x7f060026;
        public static int bright_foreground_disabled_material_light = 0x7f060027;
        public static int bright_foreground_inverse_material_dark = 0x7f060028;
        public static int bright_foreground_inverse_material_light = 0x7f060029;
        public static int bright_foreground_material_dark = 0x7f06002a;
        public static int bright_foreground_material_light = 0x7f06002b;
        public static int button_material_dark = 0x7f060031;
        public static int button_material_light = 0x7f060032;
        public static int dim_foreground_disabled_material_dark = 0x7f06006a;
        public static int dim_foreground_disabled_material_light = 0x7f06006b;
        public static int dim_foreground_material_dark = 0x7f06006c;
        public static int dim_foreground_material_light = 0x7f06006d;
        public static int error_color_material_dark = 0x7f06006e;
        public static int error_color_material_light = 0x7f06006f;
        public static int foreground_material_dark = 0x7f060078;
        public static int foreground_material_light = 0x7f060079;
        public static int highlighted_text_material_dark = 0x7f06007a;
        public static int highlighted_text_material_light = 0x7f06007b;
        public static int material_blue_grey_800 = 0x7f06022f;
        public static int material_blue_grey_900 = 0x7f060230;
        public static int material_blue_grey_950 = 0x7f060231;
        public static int material_deep_teal_200 = 0x7f060233;
        public static int material_deep_teal_500 = 0x7f060234;
        public static int material_grey_100 = 0x7f06027f;
        public static int material_grey_300 = 0x7f060280;
        public static int material_grey_50 = 0x7f060281;
        public static int material_grey_600 = 0x7f060282;
        public static int material_grey_800 = 0x7f060283;
        public static int material_grey_850 = 0x7f060284;
        public static int material_grey_900 = 0x7f060285;
        public static int notification_action_color_filter = 0x7f06030a;
        public static int notification_icon_bg_color = 0x7f06030b;
        public static int primary_dark_material_dark = 0x7f06030d;
        public static int primary_dark_material_light = 0x7f06030e;
        public static int primary_material_dark = 0x7f06030f;
        public static int primary_material_light = 0x7f060310;
        public static int primary_text_default_material_dark = 0x7f060311;
        public static int primary_text_default_material_light = 0x7f060312;
        public static int primary_text_disabled_material_dark = 0x7f060313;
        public static int primary_text_disabled_material_light = 0x7f060314;
        public static int ripple_material_dark = 0x7f060316;
        public static int ripple_material_light = 0x7f060317;
        public static int secondary_text_default_material_dark = 0x7f060318;
        public static int secondary_text_default_material_light = 0x7f060319;
        public static int secondary_text_disabled_material_dark = 0x7f06031a;
        public static int secondary_text_disabled_material_light = 0x7f06031b;
        public static int switch_thumb_disabled_material_dark = 0x7f06031c;
        public static int switch_thumb_disabled_material_light = 0x7f06031d;
        public static int switch_thumb_material_dark = 0x7f06031e;
        public static int switch_thumb_material_light = 0x7f06031f;
        public static int switch_thumb_normal_material_dark = 0x7f060320;
        public static int switch_thumb_normal_material_light = 0x7f060321;
        public static int tooltip_background_dark = 0x7f060325;
        public static int tooltip_background_light = 0x7f060326;

        private color() {
        }
    }

    public static final class dimen {
        public static int abc_action_bar_content_inset_material = 0x7f070000;
        public static int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static int abc_action_bar_default_height_material = 0x7f070002;
        public static int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static int abc_action_bar_elevation_material = 0x7f070005;
        public static int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static int abc_action_bar_stacked_max_height = 0x7f070009;
        public static int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static int abc_action_button_min_height_material = 0x7f07000d;
        public static int abc_action_button_min_width_material = 0x7f07000e;
        public static int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static int abc_alert_dialog_button_dimen = 0x7f070011;
        public static int abc_button_inset_horizontal_material = 0x7f070012;
        public static int abc_button_inset_vertical_material = 0x7f070013;
        public static int abc_button_padding_horizontal_material = 0x7f070014;
        public static int abc_button_padding_vertical_material = 0x7f070015;
        public static int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static int abc_config_prefDialogWidth = 0x7f070017;
        public static int abc_control_corner_material = 0x7f070018;
        public static int abc_control_inset_material = 0x7f070019;
        public static int abc_control_padding_material = 0x7f07001a;
        public static int abc_dialog_corner_radius_material = 0x7f07001b;
        public static int abc_dialog_fixed_height_major = 0x7f07001c;
        public static int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static int abc_dialog_fixed_width_major = 0x7f07001e;
        public static int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static int abc_dialog_min_width_major = 0x7f070022;
        public static int abc_dialog_min_width_minor = 0x7f070023;
        public static int abc_dialog_padding_material = 0x7f070024;
        public static int abc_dialog_padding_top_material = 0x7f070025;
        public static int abc_dialog_title_divider_material = 0x7f070026;
        public static int abc_disabled_alpha_material_dark = 0x7f070027;
        public static int abc_disabled_alpha_material_light = 0x7f070028;
        public static int abc_dropdownitem_icon_width = 0x7f070029;
        public static int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static int abc_edit_text_inset_top_material = 0x7f07002e;
        public static int abc_floating_window_z = 0x7f07002f;
        public static int abc_list_item_height_large_material = 0x7f070030;
        public static int abc_list_item_height_material = 0x7f070031;
        public static int abc_list_item_height_small_material = 0x7f070032;
        public static int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static int abc_panel_menu_list_width = 0x7f070034;
        public static int abc_progress_bar_height_material = 0x7f070035;
        public static int abc_search_view_preferred_height = 0x7f070036;
        public static int abc_search_view_preferred_width = 0x7f070037;
        public static int abc_seekbar_track_background_height_material = 0x7f070038;
        public static int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static int abc_star_big = 0x7f07003b;
        public static int abc_star_medium = 0x7f07003c;
        public static int abc_star_small = 0x7f07003d;
        public static int abc_switch_padding = 0x7f07003e;
        public static int abc_text_size_body_1_material = 0x7f07003f;
        public static int abc_text_size_body_2_material = 0x7f070040;
        public static int abc_text_size_button_material = 0x7f070041;
        public static int abc_text_size_caption_material = 0x7f070042;
        public static int abc_text_size_display_1_material = 0x7f070043;
        public static int abc_text_size_display_2_material = 0x7f070044;
        public static int abc_text_size_display_3_material = 0x7f070045;
        public static int abc_text_size_display_4_material = 0x7f070046;
        public static int abc_text_size_headline_material = 0x7f070047;
        public static int abc_text_size_large_material = 0x7f070048;
        public static int abc_text_size_medium_material = 0x7f070049;
        public static int abc_text_size_menu_header_material = 0x7f07004a;
        public static int abc_text_size_menu_material = 0x7f07004b;
        public static int abc_text_size_small_material = 0x7f07004c;
        public static int abc_text_size_subhead_material = 0x7f07004d;
        public static int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static int abc_text_size_title_material = 0x7f07004f;
        public static int abc_text_size_title_material_toolbar = 0x7f070050;
        public static int compat_button_inset_horizontal_material = 0x7f070058;
        public static int compat_button_inset_vertical_material = 0x7f070059;
        public static int compat_button_padding_horizontal_material = 0x7f07005a;
        public static int compat_button_padding_vertical_material = 0x7f07005b;
        public static int compat_control_corner_material = 0x7f07005c;
        public static int compat_notification_large_icon_max_height = 0x7f07005d;
        public static int compat_notification_large_icon_max_width = 0x7f07005e;
        public static int disabled_alpha_material_dark = 0x7f070091;
        public static int disabled_alpha_material_light = 0x7f070092;
        public static int highlight_alpha_material_colored = 0x7f0700b9;
        public static int highlight_alpha_material_dark = 0x7f0700ba;
        public static int highlight_alpha_material_light = 0x7f0700bb;
        public static int hint_alpha_material_dark = 0x7f0700bc;
        public static int hint_alpha_material_light = 0x7f0700bd;
        public static int hint_pressed_alpha_material_dark = 0x7f0700be;
        public static int hint_pressed_alpha_material_light = 0x7f0700bf;
        public static int notification_action_icon_size = 0x7f070332;
        public static int notification_action_text_size = 0x7f070333;
        public static int notification_big_circle_margin = 0x7f070334;
        public static int notification_content_margin_start = 0x7f070335;
        public static int notification_large_icon_height = 0x7f070336;
        public static int notification_large_icon_width = 0x7f070337;
        public static int notification_main_column_padding_top = 0x7f070338;
        public static int notification_media_narrow_margin = 0x7f070339;
        public static int notification_right_icon_size = 0x7f07033a;
        public static int notification_right_side_padding_top = 0x7f07033b;
        public static int notification_small_icon_background_padding = 0x7f07033c;
        public static int notification_small_icon_size_as_large = 0x7f07033d;
        public static int notification_subtext_size = 0x7f07033e;
        public static int notification_top_pad = 0x7f07033f;
        public static int notification_top_pad_large_text = 0x7f070340;
        public static int tooltip_corner_radius = 0x7f070341;
        public static int tooltip_horizontal_padding = 0x7f070342;
        public static int tooltip_margin = 0x7f070343;
        public static int tooltip_precise_anchor_extra_offset = 0x7f070344;
        public static int tooltip_precise_anchor_threshold = 0x7f070345;
        public static int tooltip_vertical_padding = 0x7f070346;
        public static int tooltip_y_offset_non_touch = 0x7f070347;
        public static int tooltip_y_offset_touch = 0x7f070348;

        private dimen() {
        }
    }

    public static final class drawable {
        public static int abc_ab_share_pack_mtrl_alpha = 0x7f080028;
        public static int abc_action_bar_item_background_material = 0x7f080029;
        public static int abc_btn_borderless_material = 0x7f08002a;
        public static int abc_btn_check_material = 0x7f08002b;
        public static int abc_btn_check_material_anim = 0x7f08002c;
        public static int abc_btn_check_to_on_mtrl_000 = 0x7f08002d;
        public static int abc_btn_check_to_on_mtrl_015 = 0x7f08002e;
        public static int abc_btn_colored_material = 0x7f08002f;
        public static int abc_btn_default_mtrl_shape = 0x7f080030;
        public static int abc_btn_radio_material = 0x7f080031;
        public static int abc_btn_radio_material_anim = 0x7f080032;
        public static int abc_btn_radio_to_on_mtrl_000 = 0x7f080033;
        public static int abc_btn_radio_to_on_mtrl_015 = 0x7f080034;
        public static int abc_btn_switch_to_on_mtrl_00001 = 0x7f080035;
        public static int abc_btn_switch_to_on_mtrl_00012 = 0x7f080036;
        public static int abc_cab_background_internal_bg = 0x7f080037;
        public static int abc_cab_background_top_material = 0x7f080038;
        public static int abc_cab_background_top_mtrl_alpha = 0x7f080039;
        public static int abc_control_background_material = 0x7f08003a;
        public static int abc_dialog_material_background = 0x7f08003b;
        public static int abc_edit_text_material = 0x7f08003c;
        public static int abc_ic_ab_back_material = 0x7f08003d;
        public static int abc_ic_arrow_drop_right_black_24dp = 0x7f08003e;
        public static int abc_ic_clear_material = 0x7f08003f;
        public static int abc_ic_commit_search_api_mtrl_alpha = 0x7f080040;
        public static int abc_ic_go_search_api_material = 0x7f080041;
        public static int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080042;
        public static int abc_ic_menu_cut_mtrl_alpha = 0x7f080043;
        public static int abc_ic_menu_overflow_material = 0x7f080044;
        public static int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080045;
        public static int abc_ic_menu_selectall_mtrl_alpha = 0x7f080046;
        public static int abc_ic_menu_share_mtrl_alpha = 0x7f080047;
        public static int abc_ic_search_api_material = 0x7f080048;
        public static int abc_ic_voice_search_api_material = 0x7f080049;
        public static int abc_item_background_holo_dark = 0x7f08004a;
        public static int abc_item_background_holo_light = 0x7f08004b;
        public static int abc_list_divider_material = 0x7f08004c;
        public static int abc_list_divider_mtrl_alpha = 0x7f08004d;
        public static int abc_list_focused_holo = 0x7f08004e;
        public static int abc_list_longpressed_holo = 0x7f08004f;
        public static int abc_list_pressed_holo_dark = 0x7f080050;
        public static int abc_list_pressed_holo_light = 0x7f080051;
        public static int abc_list_selector_background_transition_holo_dark = 0x7f080052;
        public static int abc_list_selector_background_transition_holo_light = 0x7f080053;
        public static int abc_list_selector_disabled_holo_dark = 0x7f080054;
        public static int abc_list_selector_disabled_holo_light = 0x7f080055;
        public static int abc_list_selector_holo_dark = 0x7f080056;
        public static int abc_list_selector_holo_light = 0x7f080057;
        public static int abc_menu_hardkey_panel_mtrl_mult = 0x7f080058;
        public static int abc_popup_background_mtrl_mult = 0x7f080059;
        public static int abc_ratingbar_indicator_material = 0x7f08005a;
        public static int abc_ratingbar_material = 0x7f08005b;
        public static int abc_ratingbar_small_material = 0x7f08005c;
        public static int abc_scrubber_control_off_mtrl_alpha = 0x7f08005d;
        public static int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005e;
        public static int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08005f;
        public static int abc_scrubber_primary_mtrl_alpha = 0x7f080060;
        public static int abc_scrubber_track_mtrl_alpha = 0x7f080061;
        public static int abc_seekbar_thumb_material = 0x7f080062;
        public static int abc_seekbar_tick_mark_material = 0x7f080063;
        public static int abc_seekbar_track_material = 0x7f080064;
        public static int abc_spinner_mtrl_am_alpha = 0x7f080065;
        public static int abc_spinner_textfield_background_material = 0x7f080066;
        public static int abc_star_black_48dp = 0x7f080067;
        public static int abc_star_half_black_48dp = 0x7f080068;
        public static int abc_switch_thumb_material = 0x7f080069;
        public static int abc_switch_track_mtrl_alpha = 0x7f08006a;
        public static int abc_tab_indicator_material = 0x7f08006b;
        public static int abc_tab_indicator_mtrl_alpha = 0x7f08006c;
        public static int abc_text_cursor_material = 0x7f08006d;
        public static int abc_text_select_handle_left_mtrl = 0x7f08006e;
        public static int abc_text_select_handle_middle_mtrl = 0x7f08006f;
        public static int abc_text_select_handle_right_mtrl = 0x7f080070;
        public static int abc_textfield_activated_mtrl_alpha = 0x7f080071;
        public static int abc_textfield_default_mtrl_alpha = 0x7f080072;
        public static int abc_textfield_search_activated_mtrl_alpha = 0x7f080073;
        public static int abc_textfield_search_default_mtrl_alpha = 0x7f080074;
        public static int abc_textfield_search_material = 0x7f080075;
        public static int abc_vector_test = 0x7f080076;
        public static int btn_checkbox_checked_mtrl = 0x7f08007f;
        public static int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080080;
        public static int btn_checkbox_unchecked_mtrl = 0x7f080081;
        public static int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080082;
        public static int btn_radio_off_mtrl = 0x7f080084;
        public static int btn_radio_off_to_on_mtrl_animation = 0x7f080085;
        public static int btn_radio_on_mtrl = 0x7f080086;
        public static int btn_radio_on_to_off_mtrl_animation = 0x7f080087;
        public static int notification_action_background = 0x7f080157;
        public static int notification_bg = 0x7f080158;
        public static int notification_bg_low = 0x7f080159;
        public static int notification_bg_low_normal = 0x7f08015a;
        public static int notification_bg_low_pressed = 0x7f08015b;
        public static int notification_bg_normal = 0x7f08015c;
        public static int notification_bg_normal_pressed = 0x7f08015d;
        public static int notification_icon_background = 0x7f08015e;
        public static int notification_template_icon_bg = 0x7f080160;
        public static int notification_template_icon_low_bg = 0x7f080161;
        public static int notification_tile_bg = 0x7f080162;
        public static int notify_panel_notification_icon_bg = 0x7f080163;
        public static int tooltip_frame_dark = 0x7f080177;
        public static int tooltip_frame_light = 0x7f080178;

        private drawable() {
        }
    }

    public static final class id {
        public static int accessibility_action_clickable_span = 0x7f0a0011;
        public static int accessibility_custom_action_0 = 0x7f0a0012;
        public static int accessibility_custom_action_1 = 0x7f0a0013;
        public static int accessibility_custom_action_10 = 0x7f0a0014;
        public static int accessibility_custom_action_11 = 0x7f0a0015;
        public static int accessibility_custom_action_12 = 0x7f0a0016;
        public static int accessibility_custom_action_13 = 0x7f0a0017;
        public static int accessibility_custom_action_14 = 0x7f0a0018;
        public static int accessibility_custom_action_15 = 0x7f0a0019;
        public static int accessibility_custom_action_16 = 0x7f0a001a;
        public static int accessibility_custom_action_17 = 0x7f0a001b;
        public static int accessibility_custom_action_18 = 0x7f0a001c;
        public static int accessibility_custom_action_19 = 0x7f0a001d;
        public static int accessibility_custom_action_2 = 0x7f0a001e;
        public static int accessibility_custom_action_20 = 0x7f0a001f;
        public static int accessibility_custom_action_21 = 0x7f0a0020;
        public static int accessibility_custom_action_22 = 0x7f0a0021;
        public static int accessibility_custom_action_23 = 0x7f0a0022;
        public static int accessibility_custom_action_24 = 0x7f0a0023;
        public static int accessibility_custom_action_25 = 0x7f0a0024;
        public static int accessibility_custom_action_26 = 0x7f0a0025;
        public static int accessibility_custom_action_27 = 0x7f0a0026;
        public static int accessibility_custom_action_28 = 0x7f0a0027;
        public static int accessibility_custom_action_29 = 0x7f0a0028;
        public static int accessibility_custom_action_3 = 0x7f0a0029;
        public static int accessibility_custom_action_30 = 0x7f0a002a;
        public static int accessibility_custom_action_31 = 0x7f0a002b;
        public static int accessibility_custom_action_4 = 0x7f0a002c;
        public static int accessibility_custom_action_5 = 0x7f0a002d;
        public static int accessibility_custom_action_6 = 0x7f0a002e;
        public static int accessibility_custom_action_7 = 0x7f0a002f;
        public static int accessibility_custom_action_8 = 0x7f0a0030;
        public static int accessibility_custom_action_9 = 0x7f0a0031;
        public static int action_bar = 0x7f0a0036;
        public static int action_bar_activity_content = 0x7f0a0037;
        public static int action_bar_container = 0x7f0a0038;
        public static int action_bar_root = 0x7f0a0039;
        public static int action_bar_spinner = 0x7f0a003a;
        public static int action_bar_subtitle = 0x7f0a003b;
        public static int action_bar_title = 0x7f0a003c;
        public static int action_container = 0x7f0a003d;
        public static int action_context_bar = 0x7f0a003e;
        public static int action_divider = 0x7f0a003f;
        public static int action_image = 0x7f0a0040;
        public static int action_menu_divider = 0x7f0a0042;
        public static int action_menu_presenter = 0x7f0a0043;
        public static int action_mode_bar = 0x7f0a0044;
        public static int action_mode_bar_stub = 0x7f0a0045;
        public static int action_mode_close_button = 0x7f0a0046;
        public static int action_text = 0x7f0a004b;
        public static int actions = 0x7f0a004c;
        public static int activity_chooser_view_content = 0x7f0a004d;
        public static int add = 0x7f0a004e;
        public static int alertTitle = 0x7f0a0053;
        public static int async = 0x7f0a005e;
        public static int blocking = 0x7f0a006a;
        public static int buttonPanel = 0x7f0a0078;
        public static int checkbox = 0x7f0a0086;
        public static int checked = 0x7f0a0087;
        public static int chronometer = 0x7f0a0088;
        public static int content = 0x7f0a0094;
        public static int contentPanel = 0x7f0a0095;
        public static int custom = 0x7f0a009f;
        public static int customPanel = 0x7f0a00a0;
        public static int decor_content_parent = 0x7f0a00a7;
        public static int default_activity_button = 0x7f0a00a8;
        public static int dialog_button = 0x7f0a00b0;
        public static int edit_query = 0x7f0a00c8;
        public static int expand_activities_button = 0x7f0a0105;
        public static int expanded_menu = 0x7f0a0106;
        public static int forever = 0x7f0a0117;
        public static int fragment_container_view_tag = 0x7f0a0119;
        public static int glide_custom_view_target_tag = 0x7f0a0120;
        public static int group_divider = 0x7f0a0124;
        public static int home = 0x7f0a012d;
        public static int icon = 0x7f0a0131;
        public static int icon_group = 0x7f0a0132;
        public static int image = 0x7f0a0137;
        public static int info = 0x7f0a0140;
        public static int italic = 0x7f0a0144;
        public static int line1 = 0x7f0a0158;
        public static int line3 = 0x7f0a0159;
        public static int listMode = 0x7f0a015d;
        public static int list_item = 0x7f0a015e;
        public static int message = 0x7f0a017d;
        public static int multiply = 0x7f0a019f;
        public static int none = 0x7f0a01ae;
        public static int normal = 0x7f0a01af;
        public static int notification_background = 0x7f0a01b4;
        public static int notification_main_column = 0x7f0a01b5;
        public static int notification_main_column_container = 0x7f0a01b6;
        public static int off = 0x7f0a01b8;
        public static int on = 0x7f0a01bc;
        public static int parentPanel = 0x7f0a01d4;
        public static int progress_circular = 0x7f0a01e5;
        public static int progress_horizontal = 0x7f0a01e6;
        public static int radio = 0x7f0a01e8;
        public static int right_icon = 0x7f0a01f6;
        public static int right_side = 0x7f0a01f7;
        public static int screen = 0x7f0a01ff;
        public static int scrollIndicatorDown = 0x7f0a0201;
        public static int scrollIndicatorUp = 0x7f0a0202;
        public static int scrollView = 0x7f0a0203;
        public static int search_badge = 0x7f0a0205;
        public static int search_bar = 0x7f0a0206;
        public static int search_button = 0x7f0a0207;
        public static int search_close_btn = 0x7f0a0208;
        public static int search_edit_frame = 0x7f0a0209;
        public static int search_go_btn = 0x7f0a020a;
        public static int search_mag_icon = 0x7f0a020b;
        public static int search_plate = 0x7f0a020c;
        public static int search_src_text = 0x7f0a020d;
        public static int search_voice_btn = 0x7f0a020e;
        public static int select_dialog_listview = 0x7f0a020f;
        public static int shortcut = 0x7f0a0215;
        public static int spacer = 0x7f0a0223;
        public static int special_effects_controller_view_tag = 0x7f0a0224;
        public static int split_action_bar = 0x7f0a0227;
        public static int src_atop = 0x7f0a022c;
        public static int src_in = 0x7f0a022d;
        public static int src_over = 0x7f0a022e;
        public static int submenuarrow = 0x7f0a0239;
        public static int submit_area = 0x7f0a023a;
        public static int tabMode = 0x7f0a0240;
        public static int tag_accessibility_actions = 0x7f0a0241;
        public static int tag_accessibility_clickable_spans = 0x7f0a0242;
        public static int tag_accessibility_heading = 0x7f0a0243;
        public static int tag_accessibility_pane_title = 0x7f0a0244;
        public static int tag_on_apply_window_listener = 0x7f0a0245;
        public static int tag_on_receive_content_listener = 0x7f0a0246;
        public static int tag_on_receive_content_mime_types = 0x7f0a0247;
        public static int tag_screen_reader_focusable = 0x7f0a0248;
        public static int tag_state_description = 0x7f0a0249;
        public static int tag_transition_group = 0x7f0a024a;
        public static int tag_unhandled_key_event_manager = 0x7f0a024b;
        public static int tag_unhandled_key_listeners = 0x7f0a024c;
        public static int tag_window_insets_animation_callback = 0x7f0a024d;
        public static int text = 0x7f0a024e;
        public static int text2 = 0x7f0a024f;
        public static int textSpacerNoButtons = 0x7f0a0251;
        public static int textSpacerNoTitle = 0x7f0a0252;
        public static int time = 0x7f0a026c;
        public static int title = 0x7f0a026d;
        public static int titleDividerNoCustom = 0x7f0a026e;
        public static int title_template = 0x7f0a0270;
        public static int topPanel = 0x7f0a0273;
        public static int unchecked = 0x7f0a0285;
        public static int uniform = 0x7f0a0286;
        public static int up = 0x7f0a0288;
        public static int view_tree_lifecycle_owner = 0x7f0a0292;
        public static int view_tree_saved_state_registry_owner = 0x7f0a0294;
        public static int view_tree_view_model_store_owner = 0x7f0a0295;
        public static int visible_removing_fragment_view_tag = 0x7f0a0297;
        public static int wrap_content = 0x7f0a02a2;

        private id() {
        }
    }

    public static final class integer {
        public static int abc_config_activityDefaultDur = 0x7f0b0000;
        public static int abc_config_activityShortDur = 0x7f0b0001;
        public static int cancel_button_image_alpha = 0x7f0b0004;
        public static int config_tooltipAnimTime = 0x7f0b0005;
        public static int status_bar_notification_info_maxnum = 0x7f0b0046;

        private integer() {
        }
    }

    public static final class interpolator {
        public static int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000;
        public static int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003;
        public static int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004;
        public static int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005;
        public static int fast_out_slow_in = 0x7f0c0006;

        private interpolator() {
        }
    }

    public static final class layout {
        public static int abc_action_bar_title_item = 0x7f0d0000;
        public static int abc_action_bar_up_container = 0x7f0d0001;
        public static int abc_action_menu_item_layout = 0x7f0d0002;
        public static int abc_action_menu_layout = 0x7f0d0003;
        public static int abc_action_mode_bar = 0x7f0d0004;
        public static int abc_action_mode_close_item_material = 0x7f0d0005;
        public static int abc_activity_chooser_view = 0x7f0d0006;
        public static int abc_activity_chooser_view_list_item = 0x7f0d0007;
        public static int abc_alert_dialog_button_bar_material = 0x7f0d0008;
        public static int abc_alert_dialog_material = 0x7f0d0009;
        public static int abc_alert_dialog_title_material = 0x7f0d000a;
        public static int abc_cascading_menu_item_layout = 0x7f0d000b;
        public static int abc_dialog_title_material = 0x7f0d000c;
        public static int abc_expanded_menu_layout = 0x7f0d000d;
        public static int abc_list_menu_item_checkbox = 0x7f0d000e;
        public static int abc_list_menu_item_icon = 0x7f0d000f;
        public static int abc_list_menu_item_layout = 0x7f0d0010;
        public static int abc_list_menu_item_radio = 0x7f0d0011;
        public static int abc_popup_menu_header_item_layout = 0x7f0d0012;
        public static int abc_popup_menu_item_layout = 0x7f0d0013;
        public static int abc_screen_content_include = 0x7f0d0014;
        public static int abc_screen_simple = 0x7f0d0015;
        public static int abc_screen_simple_overlay_action_mode = 0x7f0d0016;
        public static int abc_screen_toolbar = 0x7f0d0017;
        public static int abc_search_dropdown_item_icons_2line = 0x7f0d0018;
        public static int abc_search_view = 0x7f0d0019;
        public static int abc_select_dialog_material = 0x7f0d001a;
        public static int abc_tooltip = 0x7f0d001b;
        public static int custom_dialog = 0x7f0d0020;
        public static int notification_action = 0x7f0d0080;
        public static int notification_action_tombstone = 0x7f0d0081;
        public static int notification_template_custom_big = 0x7f0d0088;
        public static int notification_template_icon_group = 0x7f0d0089;
        public static int notification_template_part_chronometer = 0x7f0d008d;
        public static int notification_template_part_time = 0x7f0d008e;
        public static int select_dialog_item_material = 0x7f0d0093;
        public static int select_dialog_multichoice_material = 0x7f0d0094;
        public static int select_dialog_singlechoice_material = 0x7f0d0095;
        public static int support_simple_spinner_dropdown_item = 0x7f0d0096;

        private layout() {
        }
    }

    public static final class string {
        public static int abc_action_bar_home_description = 0x7f130000;
        public static int abc_action_bar_up_description = 0x7f130001;
        public static int abc_action_menu_overflow_description = 0x7f130002;
        public static int abc_action_mode_done = 0x7f130003;
        public static int abc_activity_chooser_view_see_all = 0x7f130004;
        public static int abc_activitychooserview_choose_application = 0x7f130005;
        public static int abc_capital_off = 0x7f130006;
        public static int abc_capital_on = 0x7f130007;
        public static int abc_menu_alt_shortcut_label = 0x7f130008;
        public static int abc_menu_ctrl_shortcut_label = 0x7f130009;
        public static int abc_menu_delete_shortcut_label = 0x7f13000a;
        public static int abc_menu_enter_shortcut_label = 0x7f13000b;
        public static int abc_menu_function_shortcut_label = 0x7f13000c;
        public static int abc_menu_meta_shortcut_label = 0x7f13000d;
        public static int abc_menu_shift_shortcut_label = 0x7f13000e;
        public static int abc_menu_space_shortcut_label = 0x7f13000f;
        public static int abc_menu_sym_shortcut_label = 0x7f130010;
        public static int abc_prepend_shortcut_label = 0x7f130011;
        public static int abc_search_hint = 0x7f130012;
        public static int abc_searchview_description_clear = 0x7f130013;
        public static int abc_searchview_description_query = 0x7f130014;
        public static int abc_searchview_description_search = 0x7f130015;
        public static int abc_searchview_description_submit = 0x7f130016;
        public static int abc_searchview_description_voice = 0x7f130017;
        public static int abc_shareactionprovider_share_with = 0x7f130018;
        public static int abc_shareactionprovider_share_with_application = 0x7f130019;
        public static int abc_toolbar_collapse_description = 0x7f13001a;
        public static int search_menu_title = 0x7f130117;
        public static int status_bar_notification_info_overflow = 0x7f13011f;

        private string() {
        }
    }

    public static final class style {
        public static int AlertDialog_AppCompat = 0x7f140000;
        public static int AlertDialog_AppCompat_Light = 0x7f140001;
        public static int Animation_AppCompat_Dialog = 0x7f140002;
        public static int Animation_AppCompat_DropDownUp = 0x7f140003;
        public static int Animation_AppCompat_Tooltip = 0x7f140004;
        public static int Base_AlertDialog_AppCompat = 0x7f14000b;
        public static int Base_AlertDialog_AppCompat_Light = 0x7f14000c;
        public static int Base_Animation_AppCompat_Dialog = 0x7f14000d;
        public static int Base_Animation_AppCompat_DropDownUp = 0x7f14000e;
        public static int Base_Animation_AppCompat_Tooltip = 0x7f14000f;
        public static int Base_DialogWindowTitleBackground_AppCompat = 0x7f140012;
        public static int Base_DialogWindowTitle_AppCompat = 0x7f140011;
        public static int Base_TextAppearance_AppCompat = 0x7f140016;
        public static int Base_TextAppearance_AppCompat_Body1 = 0x7f140017;
        public static int Base_TextAppearance_AppCompat_Body2 = 0x7f140018;
        public static int Base_TextAppearance_AppCompat_Button = 0x7f140019;
        public static int Base_TextAppearance_AppCompat_Caption = 0x7f14001a;
        public static int Base_TextAppearance_AppCompat_Display1 = 0x7f14001b;
        public static int Base_TextAppearance_AppCompat_Display2 = 0x7f14001c;
        public static int Base_TextAppearance_AppCompat_Display3 = 0x7f14001d;
        public static int Base_TextAppearance_AppCompat_Display4 = 0x7f14001e;
        public static int Base_TextAppearance_AppCompat_Headline = 0x7f14001f;
        public static int Base_TextAppearance_AppCompat_Inverse = 0x7f140020;
        public static int Base_TextAppearance_AppCompat_Large = 0x7f140021;
        public static int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f140022;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f140023;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f140024;
        public static int Base_TextAppearance_AppCompat_Medium = 0x7f140025;
        public static int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f140026;
        public static int Base_TextAppearance_AppCompat_Menu = 0x7f140027;
        public static int Base_TextAppearance_AppCompat_SearchResult = 0x7f140028;
        public static int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f140029;
        public static int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f14002a;
        public static int Base_TextAppearance_AppCompat_Small = 0x7f14002b;
        public static int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f14002c;
        public static int Base_TextAppearance_AppCompat_Subhead = 0x7f14002d;
        public static int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f14002e;
        public static int Base_TextAppearance_AppCompat_Title = 0x7f14002f;
        public static int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f140030;
        public static int Base_TextAppearance_AppCompat_Tooltip = 0x7f140031;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f140032;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f140033;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f140034;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f140035;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f140036;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f140037;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f140038;
        public static int Base_TextAppearance_AppCompat_Widget_Button = 0x7f140039;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f14003a;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f14003b;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f14003c;
        public static int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f14003d;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f14003e;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f14003f;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f140040;
        public static int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f140041;
        public static int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f140042;
        public static int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f140048;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f140049;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f14004a;
        public static int Base_ThemeOverlay_AppCompat = 0x7f140079;
        public static int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f14007a;
        public static int Base_ThemeOverlay_AppCompat_Dark = 0x7f14007b;
        public static int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f14007c;
        public static int Base_ThemeOverlay_AppCompat_Dialog = 0x7f14007d;
        public static int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f14007e;
        public static int Base_ThemeOverlay_AppCompat_Light = 0x7f14007f;
        public static int Base_Theme_AppCompat = 0x7f14004b;
        public static int Base_Theme_AppCompat_CompactMenu = 0x7f14004c;
        public static int Base_Theme_AppCompat_Dialog = 0x7f14004d;
        public static int Base_Theme_AppCompat_DialogWhenLarge = 0x7f140051;
        public static int Base_Theme_AppCompat_Dialog_Alert = 0x7f14004e;
        public static int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f14004f;
        public static int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f140050;
        public static int Base_Theme_AppCompat_Light = 0x7f140052;
        public static int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f140053;
        public static int Base_Theme_AppCompat_Light_Dialog = 0x7f140054;
        public static int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f140058;
        public static int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f140055;
        public static int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f140056;
        public static int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f140057;
        public static int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f1400aa;
        public static int Base_V21_Theme_AppCompat = 0x7f1400a2;
        public static int Base_V21_Theme_AppCompat_Dialog = 0x7f1400a3;
        public static int Base_V21_Theme_AppCompat_Light = 0x7f1400a4;
        public static int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f1400a5;
        public static int Base_V22_Theme_AppCompat = 0x7f1400ae;
        public static int Base_V22_Theme_AppCompat_Light = 0x7f1400af;
        public static int Base_V23_Theme_AppCompat = 0x7f1400b0;
        public static int Base_V23_Theme_AppCompat_Light = 0x7f1400b1;
        public static int Base_V26_Theme_AppCompat = 0x7f1400b6;
        public static int Base_V26_Theme_AppCompat_Light = 0x7f1400b7;
        public static int Base_V26_Widget_AppCompat_Toolbar = 0x7f1400b8;
        public static int Base_V28_Theme_AppCompat = 0x7f1400b9;
        public static int Base_V28_Theme_AppCompat_Light = 0x7f1400ba;
        public static int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1400bf;
        public static int Base_V7_Theme_AppCompat = 0x7f1400bb;
        public static int Base_V7_Theme_AppCompat_Dialog = 0x7f1400bc;
        public static int Base_V7_Theme_AppCompat_Light = 0x7f1400bd;
        public static int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1400be;
        public static int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1400c0;
        public static int Base_V7_Widget_AppCompat_EditText = 0x7f1400c1;
        public static int Base_V7_Widget_AppCompat_Toolbar = 0x7f1400c2;
        public static int Base_Widget_AppCompat_ActionBar = 0x7f1400c3;
        public static int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1400c4;
        public static int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1400c5;
        public static int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1400c6;
        public static int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1400c7;
        public static int Base_Widget_AppCompat_ActionButton = 0x7f1400c8;
        public static int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1400c9;
        public static int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1400ca;
        public static int Base_Widget_AppCompat_ActionMode = 0x7f1400cb;
        public static int Base_Widget_AppCompat_ActivityChooserView = 0x7f1400cc;
        public static int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1400cd;
        public static int Base_Widget_AppCompat_Button = 0x7f1400ce;
        public static int Base_Widget_AppCompat_ButtonBar = 0x7f1400d4;
        public static int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1400d5;
        public static int Base_Widget_AppCompat_Button_Borderless = 0x7f1400cf;
        public static int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1400d0;
        public static int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1400d1;
        public static int Base_Widget_AppCompat_Button_Colored = 0x7f1400d2;
        public static int Base_Widget_AppCompat_Button_Small = 0x7f1400d3;
        public static int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1400d6;
        public static int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1400d7;
        public static int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1400d8;
        public static int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1400d9;
        public static int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1400da;
        public static int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1400db;
        public static int Base_Widget_AppCompat_EditText = 0x7f1400dc;
        public static int Base_Widget_AppCompat_ImageButton = 0x7f1400dd;
        public static int Base_Widget_AppCompat_Light_ActionBar = 0x7f1400de;
        public static int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1400df;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1400e0;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1400e1;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1400e2;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1400e3;
        public static int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1400e4;
        public static int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1400e5;
        public static int Base_Widget_AppCompat_ListMenuView = 0x7f1400e6;
        public static int Base_Widget_AppCompat_ListPopupWindow = 0x7f1400e7;
        public static int Base_Widget_AppCompat_ListView = 0x7f1400e8;
        public static int Base_Widget_AppCompat_ListView_DropDown = 0x7f1400e9;
        public static int Base_Widget_AppCompat_ListView_Menu = 0x7f1400ea;
        public static int Base_Widget_AppCompat_PopupMenu = 0x7f1400eb;
        public static int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1400ec;
        public static int Base_Widget_AppCompat_PopupWindow = 0x7f1400ed;
        public static int Base_Widget_AppCompat_ProgressBar = 0x7f1400ee;
        public static int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1400ef;
        public static int Base_Widget_AppCompat_RatingBar = 0x7f1400f0;
        public static int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1400f1;
        public static int Base_Widget_AppCompat_RatingBar_Small = 0x7f1400f2;
        public static int Base_Widget_AppCompat_SearchView = 0x7f1400f3;
        public static int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1400f4;
        public static int Base_Widget_AppCompat_SeekBar = 0x7f1400f5;
        public static int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1400f6;
        public static int Base_Widget_AppCompat_Spinner = 0x7f1400f7;
        public static int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1400f8;
        public static int Base_Widget_AppCompat_TextView = 0x7f1400f9;
        public static int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1400fa;
        public static int Base_Widget_AppCompat_Toolbar = 0x7f1400fb;
        public static int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1400fc;
        public static int Platform_AppCompat = 0x7f140158;
        public static int Platform_AppCompat_Light = 0x7f140159;
        public static int Platform_ThemeOverlay_AppCompat = 0x7f14015e;
        public static int Platform_ThemeOverlay_AppCompat_Dark = 0x7f14015f;
        public static int Platform_ThemeOverlay_AppCompat_Light = 0x7f140160;
        public static int Platform_V21_AppCompat = 0x7f140161;
        public static int Platform_V21_AppCompat_Light = 0x7f140162;
        public static int Platform_V25_AppCompat = 0x7f140163;
        public static int Platform_V25_AppCompat_Light = 0x7f140164;
        public static int Platform_Widget_AppCompat_Spinner = 0x7f140165;
        public static int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f140166;
        public static int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f140167;
        public static int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f140168;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f140169;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f14016a;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f14016b;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f14016c;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f14016d;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f14016e;
        public static int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f140174;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f14016f;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f140170;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f140171;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f140172;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f140173;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f140175;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f140176;
        public static int TextAppearance_AppCompat = 0x7f1401b5;
        public static int TextAppearance_AppCompat_Body1 = 0x7f1401b6;
        public static int TextAppearance_AppCompat_Body2 = 0x7f1401b7;
        public static int TextAppearance_AppCompat_Button = 0x7f1401b8;
        public static int TextAppearance_AppCompat_Caption = 0x7f1401b9;
        public static int TextAppearance_AppCompat_Display1 = 0x7f1401ba;
        public static int TextAppearance_AppCompat_Display2 = 0x7f1401bb;
        public static int TextAppearance_AppCompat_Display3 = 0x7f1401bc;
        public static int TextAppearance_AppCompat_Display4 = 0x7f1401bd;
        public static int TextAppearance_AppCompat_Headline = 0x7f1401be;
        public static int TextAppearance_AppCompat_Inverse = 0x7f1401bf;
        public static int TextAppearance_AppCompat_Large = 0x7f1401c0;
        public static int TextAppearance_AppCompat_Large_Inverse = 0x7f1401c1;
        public static int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1401c2;
        public static int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1401c3;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1401c4;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1401c5;
        public static int TextAppearance_AppCompat_Medium = 0x7f1401c6;
        public static int TextAppearance_AppCompat_Medium_Inverse = 0x7f1401c7;
        public static int TextAppearance_AppCompat_Menu = 0x7f1401c8;
        public static int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1401c9;
        public static int TextAppearance_AppCompat_SearchResult_Title = 0x7f1401ca;
        public static int TextAppearance_AppCompat_Small = 0x7f1401cb;
        public static int TextAppearance_AppCompat_Small_Inverse = 0x7f1401cc;
        public static int TextAppearance_AppCompat_Subhead = 0x7f1401cd;
        public static int TextAppearance_AppCompat_Subhead_Inverse = 0x7f1401ce;
        public static int TextAppearance_AppCompat_Title = 0x7f1401cf;
        public static int TextAppearance_AppCompat_Title_Inverse = 0x7f1401d0;
        public static int TextAppearance_AppCompat_Tooltip = 0x7f1401d1;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f1401d2;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f1401d3;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f1401d4;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f1401d5;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f1401d6;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f1401d7;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f1401d8;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f1401d9;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f1401da;
        public static int TextAppearance_AppCompat_Widget_Button = 0x7f1401db;
        public static int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f1401dc;
        public static int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f1401dd;
        public static int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f1401de;
        public static int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f1401df;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f1401e0;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f1401e1;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f1401e2;
        public static int TextAppearance_AppCompat_Widget_Switch = 0x7f1401e3;
        public static int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f1401e4;
        public static int TextAppearance_Compat_Notification = 0x7f1401e5;
        public static int TextAppearance_Compat_Notification_Info = 0x7f1401e6;
        public static int TextAppearance_Compat_Notification_Line2 = 0x7f1401e8;
        public static int TextAppearance_Compat_Notification_Time = 0x7f1401eb;
        public static int TextAppearance_Compat_Notification_Title = 0x7f1401ed;
        public static int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f14022f;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f140230;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f140231;
        public static int ThemeOverlay_AppCompat = 0x7f1402a2;
        public static int ThemeOverlay_AppCompat_ActionBar = 0x7f1402a3;
        public static int ThemeOverlay_AppCompat_Dark = 0x7f1402a4;
        public static int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1402a5;
        public static int ThemeOverlay_AppCompat_DayNight = 0x7f1402a6;
        public static int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f1402a7;
        public static int ThemeOverlay_AppCompat_Dialog = 0x7f1402a8;
        public static int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f1402a9;
        public static int ThemeOverlay_AppCompat_Light = 0x7f1402aa;
        public static int Theme_AppCompat = 0x7f140232;
        public static int Theme_AppCompat_CompactMenu = 0x7f140233;
        public static int Theme_AppCompat_DayNight = 0x7f140234;
        public static int Theme_AppCompat_DayNight_DarkActionBar = 0x7f140235;
        public static int Theme_AppCompat_DayNight_Dialog = 0x7f140236;
        public static int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f140239;
        public static int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f140237;
        public static int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f140238;
        public static int Theme_AppCompat_DayNight_NoActionBar = 0x7f14023a;
        public static int Theme_AppCompat_Dialog = 0x7f14023b;
        public static int Theme_AppCompat_DialogWhenLarge = 0x7f14023e;
        public static int Theme_AppCompat_Dialog_Alert = 0x7f14023c;
        public static int Theme_AppCompat_Dialog_MinWidth = 0x7f14023d;
        public static int Theme_AppCompat_Empty = 0x7f14023f;
        public static int Theme_AppCompat_Light = 0x7f140240;
        public static int Theme_AppCompat_Light_DarkActionBar = 0x7f140241;
        public static int Theme_AppCompat_Light_Dialog = 0x7f140242;
        public static int Theme_AppCompat_Light_DialogWhenLarge = 0x7f140245;
        public static int Theme_AppCompat_Light_Dialog_Alert = 0x7f140243;
        public static int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f140244;
        public static int Theme_AppCompat_Light_NoActionBar = 0x7f140246;
        public static int Theme_AppCompat_NoActionBar = 0x7f140247;
        public static int Widget_AppCompat_ActionBar = 0x7f140314;
        public static int Widget_AppCompat_ActionBar_Solid = 0x7f140315;
        public static int Widget_AppCompat_ActionBar_TabBar = 0x7f140316;
        public static int Widget_AppCompat_ActionBar_TabText = 0x7f140317;
        public static int Widget_AppCompat_ActionBar_TabView = 0x7f140318;
        public static int Widget_AppCompat_ActionButton = 0x7f140319;
        public static int Widget_AppCompat_ActionButton_CloseMode = 0x7f14031a;
        public static int Widget_AppCompat_ActionButton_Overflow = 0x7f14031b;
        public static int Widget_AppCompat_ActionMode = 0x7f14031c;
        public static int Widget_AppCompat_ActivityChooserView = 0x7f14031d;
        public static int Widget_AppCompat_AutoCompleteTextView = 0x7f14031e;
        public static int Widget_AppCompat_Button = 0x7f14031f;
        public static int Widget_AppCompat_ButtonBar = 0x7f140325;
        public static int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f140326;
        public static int Widget_AppCompat_Button_Borderless = 0x7f140320;
        public static int Widget_AppCompat_Button_Borderless_Colored = 0x7f140321;
        public static int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f140322;
        public static int Widget_AppCompat_Button_Colored = 0x7f140323;
        public static int Widget_AppCompat_Button_Small = 0x7f140324;
        public static int Widget_AppCompat_CompoundButton_CheckBox = 0x7f140327;
        public static int Widget_AppCompat_CompoundButton_RadioButton = 0x7f140328;
        public static int Widget_AppCompat_CompoundButton_Switch = 0x7f140329;
        public static int Widget_AppCompat_DrawerArrowToggle = 0x7f14032a;
        public static int Widget_AppCompat_DropDownItem_Spinner = 0x7f14032b;
        public static int Widget_AppCompat_EditText = 0x7f14032c;
        public static int Widget_AppCompat_ImageButton = 0x7f14032d;
        public static int Widget_AppCompat_Light_ActionBar = 0x7f14032e;
        public static int Widget_AppCompat_Light_ActionBar_Solid = 0x7f14032f;
        public static int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f140330;
        public static int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f140331;
        public static int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f140332;
        public static int Widget_AppCompat_Light_ActionBar_TabText = 0x7f140333;
        public static int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f140334;
        public static int Widget_AppCompat_Light_ActionBar_TabView = 0x7f140335;
        public static int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f140336;
        public static int Widget_AppCompat_Light_ActionButton = 0x7f140337;
        public static int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f140338;
        public static int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f140339;
        public static int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f14033a;
        public static int Widget_AppCompat_Light_ActivityChooserView = 0x7f14033b;
        public static int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f14033c;
        public static int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f14033d;
        public static int Widget_AppCompat_Light_ListPopupWindow = 0x7f14033e;
        public static int Widget_AppCompat_Light_ListView_DropDown = 0x7f14033f;
        public static int Widget_AppCompat_Light_PopupMenu = 0x7f140340;
        public static int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f140341;
        public static int Widget_AppCompat_Light_SearchView = 0x7f140342;
        public static int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f140343;
        public static int Widget_AppCompat_ListMenuView = 0x7f140344;
        public static int Widget_AppCompat_ListPopupWindow = 0x7f140345;
        public static int Widget_AppCompat_ListView = 0x7f140346;
        public static int Widget_AppCompat_ListView_DropDown = 0x7f140347;
        public static int Widget_AppCompat_ListView_Menu = 0x7f140348;
        public static int Widget_AppCompat_PopupMenu = 0x7f140349;
        public static int Widget_AppCompat_PopupMenu_Overflow = 0x7f14034a;
        public static int Widget_AppCompat_PopupWindow = 0x7f14034b;
        public static int Widget_AppCompat_ProgressBar = 0x7f14034c;
        public static int Widget_AppCompat_ProgressBar_Horizontal = 0x7f14034d;
        public static int Widget_AppCompat_RatingBar = 0x7f14034e;
        public static int Widget_AppCompat_RatingBar_Indicator = 0x7f14034f;
        public static int Widget_AppCompat_RatingBar_Small = 0x7f140350;
        public static int Widget_AppCompat_SearchView = 0x7f140351;
        public static int Widget_AppCompat_SearchView_ActionBar = 0x7f140352;
        public static int Widget_AppCompat_SeekBar = 0x7f140353;
        public static int Widget_AppCompat_SeekBar_Discrete = 0x7f140354;
        public static int Widget_AppCompat_Spinner = 0x7f140355;
        public static int Widget_AppCompat_Spinner_DropDown = 0x7f140356;
        public static int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f140357;
        public static int Widget_AppCompat_Spinner_Underlined = 0x7f140358;
        public static int Widget_AppCompat_TextView = 0x7f140359;
        public static int Widget_AppCompat_TextView_SpinnerItem = 0x7f14035a;
        public static int Widget_AppCompat_Toolbar = 0x7f14035b;
        public static int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f14035c;
        public static int Widget_Compat_NotificationActionContainer = 0x7f14035d;
        public static int Widget_Compat_NotificationActionText = 0x7f14035e;

        private style() {
        }
    }

    public static final class styleable {
        public static int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static int ActionBar_background = 0x00000000;
        public static int ActionBar_backgroundSplit = 0x00000001;
        public static int ActionBar_backgroundStacked = 0x00000002;
        public static int ActionBar_contentInsetEnd = 0x00000003;
        public static int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static int ActionBar_contentInsetLeft = 0x00000005;
        public static int ActionBar_contentInsetRight = 0x00000006;
        public static int ActionBar_contentInsetStart = 0x00000007;
        public static int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static int ActionBar_customNavigationLayout = 0x00000009;
        public static int ActionBar_displayOptions = 0x0000000a;
        public static int ActionBar_divider = 0x0000000b;
        public static int ActionBar_elevation = 0x0000000c;
        public static int ActionBar_height = 0x0000000d;
        public static int ActionBar_hideOnContentScroll = 0x0000000e;
        public static int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static int ActionBar_homeLayout = 0x00000010;
        public static int ActionBar_icon = 0x00000011;
        public static int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static int ActionBar_itemPadding = 0x00000013;
        public static int ActionBar_logo = 0x00000014;
        public static int ActionBar_navigationMode = 0x00000015;
        public static int ActionBar_popupTheme = 0x00000016;
        public static int ActionBar_progressBarPadding = 0x00000017;
        public static int ActionBar_progressBarStyle = 0x00000018;
        public static int ActionBar_subtitle = 0x00000019;
        public static int ActionBar_subtitleTextStyle = 0x0000001a;
        public static int ActionBar_title = 0x0000001b;
        public static int ActionBar_titleTextStyle = 0x0000001c;
        public static int ActionMenuItemView_android_minWidth = 0x00000000;
        public static int ActionMode_background = 0x00000000;
        public static int ActionMode_backgroundSplit = 0x00000001;
        public static int ActionMode_closeItemLayout = 0x00000002;
        public static int ActionMode_height = 0x00000003;
        public static int ActionMode_subtitleTextStyle = 0x00000004;
        public static int ActionMode_titleTextStyle = 0x00000005;
        public static int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static int ActivityChooserView_initialActivityCount = 0x00000001;
        public static int AlertDialog_android_layout = 0x00000000;
        public static int AlertDialog_buttonIconDimen = 0x00000001;
        public static int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static int AlertDialog_listItemLayout = 0x00000003;
        public static int AlertDialog_listLayout = 0x00000004;
        public static int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static int AlertDialog_showTitle = 0x00000006;
        public static int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static int AppCompatImageView_android_src = 0x00000000;
        public static int AppCompatImageView_srcCompat = 0x00000001;
        public static int AppCompatImageView_tint = 0x00000002;
        public static int AppCompatImageView_tintMode = 0x00000003;
        public static int AppCompatSeekBar_android_thumb = 0x00000000;
        public static int AppCompatSeekBar_tickMark = 0x00000001;
        public static int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static int AppCompatTextView_android_textAppearance = 0x00000000;
        public static int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static int AppCompatTextView_drawableBottomCompat = 0x00000006;
        public static int AppCompatTextView_drawableEndCompat = 0x00000007;
        public static int AppCompatTextView_drawableLeftCompat = 0x00000008;
        public static int AppCompatTextView_drawableRightCompat = 0x00000009;
        public static int AppCompatTextView_drawableStartCompat = 0x0000000a;
        public static int AppCompatTextView_drawableTint = 0x0000000b;
        public static int AppCompatTextView_drawableTintMode = 0x0000000c;
        public static int AppCompatTextView_drawableTopCompat = 0x0000000d;
        public static int AppCompatTextView_emojiCompatEnabled = 0x0000000e;
        public static int AppCompatTextView_firstBaselineToTopHeight = 0x0000000f;
        public static int AppCompatTextView_fontFamily = 0x00000010;
        public static int AppCompatTextView_fontVariationSettings = 0x00000011;
        public static int AppCompatTextView_lastBaselineToBottomHeight = 0x00000012;
        public static int AppCompatTextView_lineHeight = 0x00000013;
        public static int AppCompatTextView_textAllCaps = 0x00000014;
        public static int AppCompatTextView_textLocale = 0x00000015;
        public static int AppCompatTheme_actionBarDivider = 0x00000002;
        public static int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static int AppCompatTheme_actionBarSize = 0x00000005;
        public static int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static int AppCompatTheme_actionBarStyle = 0x00000007;
        public static int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static int AppCompatTheme_actionModeBackground = 0x00000011;
        public static int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static int AppCompatTheme_actionModeCloseContentDescription = 0x00000013;
        public static int AppCompatTheme_actionModeCloseDrawable = 0x00000014;
        public static int AppCompatTheme_actionModeCopyDrawable = 0x00000015;
        public static int AppCompatTheme_actionModeCutDrawable = 0x00000016;
        public static int AppCompatTheme_actionModeFindDrawable = 0x00000017;
        public static int AppCompatTheme_actionModePasteDrawable = 0x00000018;
        public static int AppCompatTheme_actionModePopupWindowStyle = 0x00000019;
        public static int AppCompatTheme_actionModeSelectAllDrawable = 0x0000001a;
        public static int AppCompatTheme_actionModeShareDrawable = 0x0000001b;
        public static int AppCompatTheme_actionModeSplitBackground = 0x0000001c;
        public static int AppCompatTheme_actionModeStyle = 0x0000001d;
        public static int AppCompatTheme_actionModeTheme = 0x0000001e;
        public static int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001f;
        public static int AppCompatTheme_actionOverflowButtonStyle = 0x00000020;
        public static int AppCompatTheme_actionOverflowMenuStyle = 0x00000021;
        public static int AppCompatTheme_activityChooserViewStyle = 0x00000022;
        public static int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000023;
        public static int AppCompatTheme_alertDialogCenterButtons = 0x00000024;
        public static int AppCompatTheme_alertDialogStyle = 0x00000025;
        public static int AppCompatTheme_alertDialogTheme = 0x00000026;
        public static int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static int AppCompatTheme_autoCompleteTextViewStyle = 0x00000027;
        public static int AppCompatTheme_borderlessButtonStyle = 0x00000028;
        public static int AppCompatTheme_buttonBarButtonStyle = 0x00000029;
        public static int AppCompatTheme_buttonBarNegativeButtonStyle = 0x0000002a;
        public static int AppCompatTheme_buttonBarNeutralButtonStyle = 0x0000002b;
        public static int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002c;
        public static int AppCompatTheme_buttonBarStyle = 0x0000002d;
        public static int AppCompatTheme_buttonStyle = 0x0000002e;
        public static int AppCompatTheme_buttonStyleSmall = 0x0000002f;
        public static int AppCompatTheme_checkboxStyle = 0x00000030;
        public static int AppCompatTheme_checkedTextViewStyle = 0x00000031;
        public static int AppCompatTheme_colorAccent = 0x00000032;
        public static int AppCompatTheme_colorBackgroundFloating = 0x00000033;
        public static int AppCompatTheme_colorButtonNormal = 0x00000034;
        public static int AppCompatTheme_colorControlActivated = 0x00000035;
        public static int AppCompatTheme_colorControlHighlight = 0x00000036;
        public static int AppCompatTheme_colorControlNormal = 0x00000037;
        public static int AppCompatTheme_colorError = 0x00000038;
        public static int AppCompatTheme_colorPrimary = 0x00000039;
        public static int AppCompatTheme_colorPrimaryDark = 0x0000003a;
        public static int AppCompatTheme_colorSwitchThumbNormal = 0x0000003b;
        public static int AppCompatTheme_controlBackground = 0x0000003c;
        public static int AppCompatTheme_dialogCornerRadius = 0x0000003d;
        public static int AppCompatTheme_dialogPreferredPadding = 0x0000003e;
        public static int AppCompatTheme_dialogTheme = 0x0000003f;
        public static int AppCompatTheme_dividerHorizontal = 0x00000040;
        public static int AppCompatTheme_dividerVertical = 0x00000041;
        public static int AppCompatTheme_dropDownListViewStyle = 0x00000042;
        public static int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000043;
        public static int AppCompatTheme_editTextBackground = 0x00000044;
        public static int AppCompatTheme_editTextColor = 0x00000045;
        public static int AppCompatTheme_editTextStyle = 0x00000046;
        public static int AppCompatTheme_homeAsUpIndicator = 0x00000047;
        public static int AppCompatTheme_imageButtonStyle = 0x00000048;
        public static int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000049;
        public static int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 0x0000004a;
        public static int AppCompatTheme_listChoiceIndicatorSingleAnimated = 0x0000004b;
        public static int AppCompatTheme_listDividerAlertDialog = 0x0000004c;
        public static int AppCompatTheme_listMenuViewStyle = 0x0000004d;
        public static int AppCompatTheme_listPopupWindowStyle = 0x0000004e;
        public static int AppCompatTheme_listPreferredItemHeight = 0x0000004f;
        public static int AppCompatTheme_listPreferredItemHeightLarge = 0x00000050;
        public static int AppCompatTheme_listPreferredItemHeightSmall = 0x00000051;
        public static int AppCompatTheme_listPreferredItemPaddingEnd = 0x00000052;
        public static int AppCompatTheme_listPreferredItemPaddingLeft = 0x00000053;
        public static int AppCompatTheme_listPreferredItemPaddingRight = 0x00000054;
        public static int AppCompatTheme_listPreferredItemPaddingStart = 0x00000055;
        public static int AppCompatTheme_panelBackground = 0x00000056;
        public static int AppCompatTheme_panelMenuListTheme = 0x00000057;
        public static int AppCompatTheme_panelMenuListWidth = 0x00000058;
        public static int AppCompatTheme_popupMenuStyle = 0x00000059;
        public static int AppCompatTheme_popupWindowStyle = 0x0000005a;
        public static int AppCompatTheme_radioButtonStyle = 0x0000005b;
        public static int AppCompatTheme_ratingBarStyle = 0x0000005c;
        public static int AppCompatTheme_ratingBarStyleIndicator = 0x0000005d;
        public static int AppCompatTheme_ratingBarStyleSmall = 0x0000005e;
        public static int AppCompatTheme_searchViewStyle = 0x0000005f;
        public static int AppCompatTheme_seekBarStyle = 0x00000060;
        public static int AppCompatTheme_selectableItemBackground = 0x00000061;
        public static int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000062;
        public static int AppCompatTheme_spinnerDropDownItemStyle = 0x00000063;
        public static int AppCompatTheme_spinnerStyle = 0x00000064;
        public static int AppCompatTheme_switchStyle = 0x00000065;
        public static int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000066;
        public static int AppCompatTheme_textAppearanceListItem = 0x00000067;
        public static int AppCompatTheme_textAppearanceListItemSecondary = 0x00000068;
        public static int AppCompatTheme_textAppearanceListItemSmall = 0x00000069;
        public static int AppCompatTheme_textAppearancePopupMenuHeader = 0x0000006a;
        public static int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x0000006b;
        public static int AppCompatTheme_textAppearanceSearchResultTitle = 0x0000006c;
        public static int AppCompatTheme_textAppearanceSmallPopupMenu = 0x0000006d;
        public static int AppCompatTheme_textColorAlertDialogListItem = 0x0000006e;
        public static int AppCompatTheme_textColorSearchUrl = 0x0000006f;
        public static int AppCompatTheme_toolbarNavigationButtonStyle = 0x00000070;
        public static int AppCompatTheme_toolbarStyle = 0x00000071;
        public static int AppCompatTheme_tooltipForegroundColor = 0x00000072;
        public static int AppCompatTheme_tooltipFrameBackground = 0x00000073;
        public static int AppCompatTheme_viewInflaterClass = 0x00000074;
        public static int AppCompatTheme_windowActionBar = 0x00000075;
        public static int AppCompatTheme_windowActionBarOverlay = 0x00000076;
        public static int AppCompatTheme_windowActionModeOverlay = 0x00000077;
        public static int AppCompatTheme_windowFixedHeightMajor = 0x00000078;
        public static int AppCompatTheme_windowFixedHeightMinor = 0x00000079;
        public static int AppCompatTheme_windowFixedWidthMajor = 0x0000007a;
        public static int AppCompatTheme_windowFixedWidthMinor = 0x0000007b;
        public static int AppCompatTheme_windowMinWidthMajor = 0x0000007c;
        public static int AppCompatTheme_windowMinWidthMinor = 0x0000007d;
        public static int AppCompatTheme_windowNoTitle = 0x0000007e;
        public static int ButtonBarLayout_allowStacking = 0x00000000;
        public static int ColorStateListItem_alpha = 0x00000003;
        public static int ColorStateListItem_android_alpha = 0x00000001;
        public static int ColorStateListItem_android_color = 0x00000000;
        public static int ColorStateListItem_android_lStar = 0x00000002;
        public static int ColorStateListItem_lStar = 0x00000004;
        public static int CompoundButton_android_button = 0x00000000;
        public static int CompoundButton_buttonCompat = 0x00000001;
        public static int CompoundButton_buttonTint = 0x00000002;
        public static int CompoundButton_buttonTintMode = 0x00000003;
        public static int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static int DrawerArrowToggle_barLength = 0x00000002;
        public static int DrawerArrowToggle_color = 0x00000003;
        public static int DrawerArrowToggle_drawableSize = 0x00000004;
        public static int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static int DrawerArrowToggle_spinBars = 0x00000006;
        public static int DrawerArrowToggle_thickness = 0x00000007;
        public static int FontFamilyFont_android_font = 0x00000000;
        public static int FontFamilyFont_android_fontStyle = 0x00000002;
        public static int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static int FontFamilyFont_android_fontWeight = 0x00000001;
        public static int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static int FontFamilyFont_font = 0x00000005;
        public static int FontFamilyFont_fontStyle = 0x00000006;
        public static int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static int FontFamilyFont_fontWeight = 0x00000008;
        public static int FontFamilyFont_ttcIndex = 0x00000009;
        public static int FontFamily_fontProviderAuthority = 0x00000000;
        public static int FontFamily_fontProviderCerts = 0x00000001;
        public static int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static int FontFamily_fontProviderPackage = 0x00000004;
        public static int FontFamily_fontProviderQuery = 0x00000005;
        public static int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static int FragmentContainerView_android_name = 0x00000000;
        public static int FragmentContainerView_android_tag = 0x00000001;
        public static int Fragment_android_id = 0x00000001;
        public static int Fragment_android_name = 0x00000000;
        public static int Fragment_android_tag = 0x00000002;
        public static int GradientColorItem_android_color = 0x00000000;
        public static int GradientColorItem_android_offset = 0x00000001;
        public static int GradientColor_android_centerColor = 0x00000007;
        public static int GradientColor_android_centerX = 0x00000003;
        public static int GradientColor_android_centerY = 0x00000004;
        public static int GradientColor_android_endColor = 0x00000001;
        public static int GradientColor_android_endX = 0x0000000a;
        public static int GradientColor_android_endY = 0x0000000b;
        public static int GradientColor_android_gradientRadius = 0x00000005;
        public static int GradientColor_android_startColor = 0x00000000;
        public static int GradientColor_android_startX = 0x00000008;
        public static int GradientColor_android_startY = 0x00000009;
        public static int GradientColor_android_tileMode = 0x00000006;
        public static int GradientColor_android_type = 0x00000002;
        public static int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static int LinearLayoutCompat_android_gravity = 0x00000000;
        public static int LinearLayoutCompat_android_orientation = 0x00000001;
        public static int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static int LinearLayoutCompat_divider = 0x00000005;
        public static int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static int LinearLayoutCompat_showDividers = 0x00000008;
        public static int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static int MenuGroup_android_checkableBehavior = 0x00000005;
        public static int MenuGroup_android_enabled = 0x00000000;
        public static int MenuGroup_android_id = 0x00000001;
        public static int MenuGroup_android_menuCategory = 0x00000003;
        public static int MenuGroup_android_orderInCategory = 0x00000004;
        public static int MenuGroup_android_visible = 0x00000002;
        public static int MenuItem_actionLayout = 0x0000000d;
        public static int MenuItem_actionProviderClass = 0x0000000e;
        public static int MenuItem_actionViewClass = 0x0000000f;
        public static int MenuItem_alphabeticModifiers = 0x00000010;
        public static int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static int MenuItem_android_checkable = 0x0000000b;
        public static int MenuItem_android_checked = 0x00000003;
        public static int MenuItem_android_enabled = 0x00000001;
        public static int MenuItem_android_icon = 0x00000000;
        public static int MenuItem_android_id = 0x00000002;
        public static int MenuItem_android_menuCategory = 0x00000005;
        public static int MenuItem_android_numericShortcut = 0x0000000a;
        public static int MenuItem_android_onClick = 0x0000000c;
        public static int MenuItem_android_orderInCategory = 0x00000006;
        public static int MenuItem_android_title = 0x00000007;
        public static int MenuItem_android_titleCondensed = 0x00000008;
        public static int MenuItem_android_visible = 0x00000004;
        public static int MenuItem_contentDescription = 0x00000011;
        public static int MenuItem_iconTint = 0x00000012;
        public static int MenuItem_iconTintMode = 0x00000013;
        public static int MenuItem_numericModifiers = 0x00000014;
        public static int MenuItem_showAsAction = 0x00000015;
        public static int MenuItem_tooltipText = 0x00000016;
        public static int MenuView_android_headerBackground = 0x00000004;
        public static int MenuView_android_horizontalDivider = 0x00000002;
        public static int MenuView_android_itemBackground = 0x00000005;
        public static int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static int MenuView_android_itemTextAppearance = 0x00000001;
        public static int MenuView_android_verticalDivider = 0x00000003;
        public static int MenuView_android_windowAnimationStyle = 0x00000000;
        public static int MenuView_preserveIconSpacing = 0x00000007;
        public static int MenuView_subMenuArrow = 0x00000008;
        public static int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static int PopupWindow_android_popupBackground = 0x00000000;
        public static int PopupWindow_overlapAnchor = 0x00000002;
        public static int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static int SearchView_android_focusable = 0x00000001;
        public static int SearchView_android_hint = 0x00000004;
        public static int SearchView_android_imeOptions = 0x00000006;
        public static int SearchView_android_inputType = 0x00000005;
        public static int SearchView_android_maxWidth = 0x00000002;
        public static int SearchView_android_text = 0x00000003;
        public static int SearchView_android_textAppearance = 0x00000000;
        public static int SearchView_animateMenuItems = 0x00000007;
        public static int SearchView_animateNavigationIcon = 0x00000008;
        public static int SearchView_autoShowKeyboard = 0x00000009;
        public static int SearchView_backHandlingEnabled = 0x0000000a;
        public static int SearchView_backgroundTint = 0x0000000b;
        public static int SearchView_closeIcon = 0x0000000c;
        public static int SearchView_commitIcon = 0x0000000d;
        public static int SearchView_defaultQueryHint = 0x0000000e;
        public static int SearchView_goIcon = 0x0000000f;
        public static int SearchView_headerLayout = 0x00000010;
        public static int SearchView_hideNavigationIcon = 0x00000011;
        public static int SearchView_iconifiedByDefault = 0x00000012;
        public static int SearchView_layout = 0x00000013;
        public static int SearchView_queryBackground = 0x00000014;
        public static int SearchView_queryHint = 0x00000015;
        public static int SearchView_searchHintIcon = 0x00000016;
        public static int SearchView_searchIcon = 0x00000017;
        public static int SearchView_searchPrefixText = 0x00000018;
        public static int SearchView_submitBackground = 0x00000019;
        public static int SearchView_suggestionRowLayout = 0x0000001a;
        public static int SearchView_useDrawerArrowDrawable = 0x0000001b;
        public static int SearchView_voiceIcon = 0x0000001c;
        public static int Spinner_android_dropDownWidth = 0x00000003;
        public static int Spinner_android_entries = 0x00000000;
        public static int Spinner_android_popupBackground = 0x00000001;
        public static int Spinner_android_prompt = 0x00000002;
        public static int Spinner_popupTheme = 0x00000004;
        public static int StateListDrawableItem_android_drawable = 0x00000000;
        public static int StateListDrawable_android_constantSize = 0x00000003;
        public static int StateListDrawable_android_dither = 0x00000000;
        public static int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static int StateListDrawable_android_variablePadding = 0x00000002;
        public static int StateListDrawable_android_visible = 0x00000001;
        public static int SwitchCompat_android_textOff = 0x00000001;
        public static int SwitchCompat_android_textOn = 0x00000000;
        public static int SwitchCompat_android_thumb = 0x00000002;
        public static int SwitchCompat_showText = 0x00000003;
        public static int SwitchCompat_splitTrack = 0x00000004;
        public static int SwitchCompat_switchMinWidth = 0x00000005;
        public static int SwitchCompat_switchPadding = 0x00000006;
        public static int SwitchCompat_switchTextAppearance = 0x00000007;
        public static int SwitchCompat_thumbTextPadding = 0x00000008;
        public static int SwitchCompat_thumbTint = 0x00000009;
        public static int SwitchCompat_thumbTintMode = 0x0000000a;
        public static int SwitchCompat_track = 0x0000000b;
        public static int SwitchCompat_trackTint = 0x0000000c;
        public static int SwitchCompat_trackTintMode = 0x0000000d;
        public static int TextAppearance_android_fontFamily = 0x0000000a;
        public static int TextAppearance_android_shadowColor = 0x00000006;
        public static int TextAppearance_android_shadowDx = 0x00000007;
        public static int TextAppearance_android_shadowDy = 0x00000008;
        public static int TextAppearance_android_shadowRadius = 0x00000009;
        public static int TextAppearance_android_textColor = 0x00000003;
        public static int TextAppearance_android_textColorHint = 0x00000004;
        public static int TextAppearance_android_textColorLink = 0x00000005;
        public static int TextAppearance_android_textFontWeight = 0x0000000b;
        public static int TextAppearance_android_textSize = 0x00000000;
        public static int TextAppearance_android_textStyle = 0x00000002;
        public static int TextAppearance_android_typeface = 0x00000001;
        public static int TextAppearance_fontFamily = 0x0000000c;
        public static int TextAppearance_fontVariationSettings = 0x0000000d;
        public static int TextAppearance_textAllCaps = 0x0000000e;
        public static int TextAppearance_textLocale = 0x0000000f;
        public static int Toolbar_android_gravity = 0x00000000;
        public static int Toolbar_android_minHeight = 0x00000001;
        public static int Toolbar_buttonGravity = 0x00000002;
        public static int Toolbar_collapseContentDescription = 0x00000003;
        public static int Toolbar_collapseIcon = 0x00000004;
        public static int Toolbar_contentInsetEnd = 0x00000005;
        public static int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static int Toolbar_contentInsetLeft = 0x00000007;
        public static int Toolbar_contentInsetRight = 0x00000008;
        public static int Toolbar_contentInsetStart = 0x00000009;
        public static int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static int Toolbar_logo = 0x0000000b;
        public static int Toolbar_logoDescription = 0x0000000c;
        public static int Toolbar_maxButtonHeight = 0x0000000d;
        public static int Toolbar_menu = 0x0000000e;
        public static int Toolbar_navigationContentDescription = 0x0000000f;
        public static int Toolbar_navigationIcon = 0x00000010;
        public static int Toolbar_popupTheme = 0x00000011;
        public static int Toolbar_subtitle = 0x00000012;
        public static int Toolbar_subtitleTextAppearance = 0x00000013;
        public static int Toolbar_subtitleTextColor = 0x00000014;
        public static int Toolbar_title = 0x00000015;
        public static int Toolbar_titleMargin = 0x00000016;
        public static int Toolbar_titleMarginBottom = 0x00000017;
        public static int Toolbar_titleMarginEnd = 0x00000018;
        public static int Toolbar_titleMarginStart = 0x00000019;
        public static int Toolbar_titleMarginTop = 0x0000001a;
        public static int Toolbar_titleMargins = 0x0000001b;
        public static int Toolbar_titleTextAppearance = 0x0000001c;
        public static int Toolbar_titleTextColor = 0x0000001d;
        public static int ViewBackgroundHelper_android_background = 0x00000000;
        public static int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static int ViewStubCompat_android_id = 0x00000000;
        public static int ViewStubCompat_android_inflatedId = 0x00000002;
        public static int ViewStubCompat_android_layout = 0x00000001;
        public static int View_android_focusable = 0x00000001;
        public static int View_android_theme = 0x00000000;
        public static int View_paddingEnd = 0x00000002;
        public static int View_paddingStart = 0x00000003;
        public static int View_theme = 0x00000004;
        public static int[] ActionBar = {com.proxy.videowidget.R.attr.background, com.proxy.videowidget.R.attr.backgroundSplit, com.proxy.videowidget.R.attr.backgroundStacked, com.proxy.videowidget.R.attr.contentInsetEnd, com.proxy.videowidget.R.attr.contentInsetEndWithActions, com.proxy.videowidget.R.attr.contentInsetLeft, com.proxy.videowidget.R.attr.contentInsetRight, com.proxy.videowidget.R.attr.contentInsetStart, com.proxy.videowidget.R.attr.contentInsetStartWithNavigation, com.proxy.videowidget.R.attr.customNavigationLayout, com.proxy.videowidget.R.attr.displayOptions, com.proxy.videowidget.R.attr.divider, com.proxy.videowidget.R.attr.elevation, com.proxy.videowidget.R.attr.height, com.proxy.videowidget.R.attr.hideOnContentScroll, com.proxy.videowidget.R.attr.homeAsUpIndicator, com.proxy.videowidget.R.attr.homeLayout, com.proxy.videowidget.R.attr.icon, com.proxy.videowidget.R.attr.indeterminateProgressStyle, com.proxy.videowidget.R.attr.itemPadding, com.proxy.videowidget.R.attr.logo, com.proxy.videowidget.R.attr.navigationMode, com.proxy.videowidget.R.attr.popupTheme, com.proxy.videowidget.R.attr.progressBarPadding, com.proxy.videowidget.R.attr.progressBarStyle, com.proxy.videowidget.R.attr.subtitle, com.proxy.videowidget.R.attr.subtitleTextStyle, com.proxy.videowidget.R.attr.title, com.proxy.videowidget.R.attr.titleTextStyle};
        public static int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static int[] ActionMenuView = new int[0];
        public static int[] ActionMode = {com.proxy.videowidget.R.attr.background, com.proxy.videowidget.R.attr.backgroundSplit, com.proxy.videowidget.R.attr.closeItemLayout, com.proxy.videowidget.R.attr.height, com.proxy.videowidget.R.attr.subtitleTextStyle, com.proxy.videowidget.R.attr.titleTextStyle};
        public static int[] ActivityChooserView = {com.proxy.videowidget.R.attr.expandActivityOverflowButtonDrawable, com.proxy.videowidget.R.attr.initialActivityCount};
        public static int[] AlertDialog = {android.R.attr.layout, com.proxy.videowidget.R.attr.buttonIconDimen, com.proxy.videowidget.R.attr.buttonPanelSideLayout, com.proxy.videowidget.R.attr.listItemLayout, com.proxy.videowidget.R.attr.listLayout, com.proxy.videowidget.R.attr.multiChoiceItemLayout, com.proxy.videowidget.R.attr.showTitle, com.proxy.videowidget.R.attr.singleChoiceItemLayout};
        public static int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static int[] AppCompatImageView = {android.R.attr.src, com.proxy.videowidget.R.attr.srcCompat, com.proxy.videowidget.R.attr.tint, com.proxy.videowidget.R.attr.tintMode};
        public static int[] AppCompatSeekBar = {android.R.attr.thumb, com.proxy.videowidget.R.attr.tickMark, com.proxy.videowidget.R.attr.tickMarkTint, com.proxy.videowidget.R.attr.tickMarkTintMode};
        public static int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static int[] AppCompatTextView = {android.R.attr.textAppearance, com.proxy.videowidget.R.attr.autoSizeMaxTextSize, com.proxy.videowidget.R.attr.autoSizeMinTextSize, com.proxy.videowidget.R.attr.autoSizePresetSizes, com.proxy.videowidget.R.attr.autoSizeStepGranularity, com.proxy.videowidget.R.attr.autoSizeTextType, com.proxy.videowidget.R.attr.drawableBottomCompat, com.proxy.videowidget.R.attr.drawableEndCompat, com.proxy.videowidget.R.attr.drawableLeftCompat, com.proxy.videowidget.R.attr.drawableRightCompat, com.proxy.videowidget.R.attr.drawableStartCompat, com.proxy.videowidget.R.attr.drawableTint, com.proxy.videowidget.R.attr.drawableTintMode, com.proxy.videowidget.R.attr.drawableTopCompat, com.proxy.videowidget.R.attr.emojiCompatEnabled, com.proxy.videowidget.R.attr.firstBaselineToTopHeight, com.proxy.videowidget.R.attr.fontFamily, com.proxy.videowidget.R.attr.fontVariationSettings, com.proxy.videowidget.R.attr.lastBaselineToBottomHeight, com.proxy.videowidget.R.attr.lineHeight, com.proxy.videowidget.R.attr.textAllCaps, com.proxy.videowidget.R.attr.textLocale};
        public static int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, com.proxy.videowidget.R.attr.actionBarDivider, com.proxy.videowidget.R.attr.actionBarItemBackground, com.proxy.videowidget.R.attr.actionBarPopupTheme, com.proxy.videowidget.R.attr.actionBarSize, com.proxy.videowidget.R.attr.actionBarSplitStyle, com.proxy.videowidget.R.attr.actionBarStyle, com.proxy.videowidget.R.attr.actionBarTabBarStyle, com.proxy.videowidget.R.attr.actionBarTabStyle, com.proxy.videowidget.R.attr.actionBarTabTextStyle, com.proxy.videowidget.R.attr.actionBarTheme, com.proxy.videowidget.R.attr.actionBarWidgetTheme, com.proxy.videowidget.R.attr.actionButtonStyle, com.proxy.videowidget.R.attr.actionDropDownStyle, com.proxy.videowidget.R.attr.actionMenuTextAppearance, com.proxy.videowidget.R.attr.actionMenuTextColor, com.proxy.videowidget.R.attr.actionModeBackground, com.proxy.videowidget.R.attr.actionModeCloseButtonStyle, com.proxy.videowidget.R.attr.actionModeCloseContentDescription, com.proxy.videowidget.R.attr.actionModeCloseDrawable, com.proxy.videowidget.R.attr.actionModeCopyDrawable, com.proxy.videowidget.R.attr.actionModeCutDrawable, com.proxy.videowidget.R.attr.actionModeFindDrawable, com.proxy.videowidget.R.attr.actionModePasteDrawable, com.proxy.videowidget.R.attr.actionModePopupWindowStyle, com.proxy.videowidget.R.attr.actionModeSelectAllDrawable, com.proxy.videowidget.R.attr.actionModeShareDrawable, com.proxy.videowidget.R.attr.actionModeSplitBackground, com.proxy.videowidget.R.attr.actionModeStyle, com.proxy.videowidget.R.attr.actionModeTheme, com.proxy.videowidget.R.attr.actionModeWebSearchDrawable, com.proxy.videowidget.R.attr.actionOverflowButtonStyle, com.proxy.videowidget.R.attr.actionOverflowMenuStyle, com.proxy.videowidget.R.attr.activityChooserViewStyle, com.proxy.videowidget.R.attr.alertDialogButtonGroupStyle, com.proxy.videowidget.R.attr.alertDialogCenterButtons, com.proxy.videowidget.R.attr.alertDialogStyle, com.proxy.videowidget.R.attr.alertDialogTheme, com.proxy.videowidget.R.attr.autoCompleteTextViewStyle, com.proxy.videowidget.R.attr.borderlessButtonStyle, com.proxy.videowidget.R.attr.buttonBarButtonStyle, com.proxy.videowidget.R.attr.buttonBarNegativeButtonStyle, com.proxy.videowidget.R.attr.buttonBarNeutralButtonStyle, com.proxy.videowidget.R.attr.buttonBarPositiveButtonStyle, com.proxy.videowidget.R.attr.buttonBarStyle, com.proxy.videowidget.R.attr.buttonStyle, com.proxy.videowidget.R.attr.buttonStyleSmall, com.proxy.videowidget.R.attr.checkboxStyle, com.proxy.videowidget.R.attr.checkedTextViewStyle, com.proxy.videowidget.R.attr.colorAccent, com.proxy.videowidget.R.attr.colorBackgroundFloating, com.proxy.videowidget.R.attr.colorButtonNormal, com.proxy.videowidget.R.attr.colorControlActivated, com.proxy.videowidget.R.attr.colorControlHighlight, com.proxy.videowidget.R.attr.colorControlNormal, com.proxy.videowidget.R.attr.colorError, com.proxy.videowidget.R.attr.colorPrimary, com.proxy.videowidget.R.attr.colorPrimaryDark, com.proxy.videowidget.R.attr.colorSwitchThumbNormal, com.proxy.videowidget.R.attr.controlBackground, com.proxy.videowidget.R.attr.dialogCornerRadius, com.proxy.videowidget.R.attr.dialogPreferredPadding, com.proxy.videowidget.R.attr.dialogTheme, com.proxy.videowidget.R.attr.dividerHorizontal, com.proxy.videowidget.R.attr.dividerVertical, com.proxy.videowidget.R.attr.dropDownListViewStyle, com.proxy.videowidget.R.attr.dropdownListPreferredItemHeight, com.proxy.videowidget.R.attr.editTextBackground, com.proxy.videowidget.R.attr.editTextColor, com.proxy.videowidget.R.attr.editTextStyle, com.proxy.videowidget.R.attr.homeAsUpIndicator, com.proxy.videowidget.R.attr.imageButtonStyle, com.proxy.videowidget.R.attr.listChoiceBackgroundIndicator, com.proxy.videowidget.R.attr.listChoiceIndicatorMultipleAnimated, com.proxy.videowidget.R.attr.listChoiceIndicatorSingleAnimated, com.proxy.videowidget.R.attr.listDividerAlertDialog, com.proxy.videowidget.R.attr.listMenuViewStyle, com.proxy.videowidget.R.attr.listPopupWindowStyle, com.proxy.videowidget.R.attr.listPreferredItemHeight, com.proxy.videowidget.R.attr.listPreferredItemHeightLarge, com.proxy.videowidget.R.attr.listPreferredItemHeightSmall, com.proxy.videowidget.R.attr.listPreferredItemPaddingEnd, com.proxy.videowidget.R.attr.listPreferredItemPaddingLeft, com.proxy.videowidget.R.attr.listPreferredItemPaddingRight, com.proxy.videowidget.R.attr.listPreferredItemPaddingStart, com.proxy.videowidget.R.attr.panelBackground, com.proxy.videowidget.R.attr.panelMenuListTheme, com.proxy.videowidget.R.attr.panelMenuListWidth, com.proxy.videowidget.R.attr.popupMenuStyle, com.proxy.videowidget.R.attr.popupWindowStyle, com.proxy.videowidget.R.attr.radioButtonStyle, com.proxy.videowidget.R.attr.ratingBarStyle, com.proxy.videowidget.R.attr.ratingBarStyleIndicator, com.proxy.videowidget.R.attr.ratingBarStyleSmall, com.proxy.videowidget.R.attr.searchViewStyle, com.proxy.videowidget.R.attr.seekBarStyle, com.proxy.videowidget.R.attr.selectableItemBackground, com.proxy.videowidget.R.attr.selectableItemBackgroundBorderless, com.proxy.videowidget.R.attr.spinnerDropDownItemStyle, com.proxy.videowidget.R.attr.spinnerStyle, com.proxy.videowidget.R.attr.switchStyle, com.proxy.videowidget.R.attr.textAppearanceLargePopupMenu, com.proxy.videowidget.R.attr.textAppearanceListItem, com.proxy.videowidget.R.attr.textAppearanceListItemSecondary, com.proxy.videowidget.R.attr.textAppearanceListItemSmall, com.proxy.videowidget.R.attr.textAppearancePopupMenuHeader, com.proxy.videowidget.R.attr.textAppearanceSearchResultSubtitle, com.proxy.videowidget.R.attr.textAppearanceSearchResultTitle, com.proxy.videowidget.R.attr.textAppearanceSmallPopupMenu, com.proxy.videowidget.R.attr.textColorAlertDialogListItem, com.proxy.videowidget.R.attr.textColorSearchUrl, com.proxy.videowidget.R.attr.toolbarNavigationButtonStyle, com.proxy.videowidget.R.attr.toolbarStyle, com.proxy.videowidget.R.attr.tooltipForegroundColor, com.proxy.videowidget.R.attr.tooltipFrameBackground, com.proxy.videowidget.R.attr.viewInflaterClass, com.proxy.videowidget.R.attr.windowActionBar, com.proxy.videowidget.R.attr.windowActionBarOverlay, com.proxy.videowidget.R.attr.windowActionModeOverlay, com.proxy.videowidget.R.attr.windowFixedHeightMajor, com.proxy.videowidget.R.attr.windowFixedHeightMinor, com.proxy.videowidget.R.attr.windowFixedWidthMajor, com.proxy.videowidget.R.attr.windowFixedWidthMinor, com.proxy.videowidget.R.attr.windowMinWidthMajor, com.proxy.videowidget.R.attr.windowMinWidthMinor, com.proxy.videowidget.R.attr.windowNoTitle};
        public static int[] ButtonBarLayout = {com.proxy.videowidget.R.attr.allowStacking};
        public static int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, com.proxy.videowidget.R.attr.alpha, com.proxy.videowidget.R.attr.lStar};
        public static int[] CompoundButton = {android.R.attr.button, com.proxy.videowidget.R.attr.buttonCompat, com.proxy.videowidget.R.attr.buttonTint, com.proxy.videowidget.R.attr.buttonTintMode};
        public static int[] DrawerArrowToggle = {com.proxy.videowidget.R.attr.arrowHeadLength, com.proxy.videowidget.R.attr.arrowShaftLength, com.proxy.videowidget.R.attr.barLength, com.proxy.videowidget.R.attr.color, com.proxy.videowidget.R.attr.drawableSize, com.proxy.videowidget.R.attr.gapBetweenBars, com.proxy.videowidget.R.attr.spinBars, com.proxy.videowidget.R.attr.thickness};
        public static int[] FontFamily = {com.proxy.videowidget.R.attr.fontProviderAuthority, com.proxy.videowidget.R.attr.fontProviderCerts, com.proxy.videowidget.R.attr.fontProviderFetchStrategy, com.proxy.videowidget.R.attr.fontProviderFetchTimeout, com.proxy.videowidget.R.attr.fontProviderPackage, com.proxy.videowidget.R.attr.fontProviderQuery, com.proxy.videowidget.R.attr.fontProviderSystemFontFamily};
        public static int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, com.proxy.videowidget.R.attr.font, com.proxy.videowidget.R.attr.fontStyle, com.proxy.videowidget.R.attr.fontVariationSettings, com.proxy.videowidget.R.attr.fontWeight, com.proxy.videowidget.R.attr.ttcIndex};
        public static int[] Fragment = {android.R.attr.name, android.R.attr.id, android.R.attr.tag};
        public static int[] FragmentContainerView = {android.R.attr.name, android.R.attr.tag};
        public static int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, com.proxy.videowidget.R.attr.divider, com.proxy.videowidget.R.attr.dividerPadding, com.proxy.videowidget.R.attr.measureWithLargestChild, com.proxy.videowidget.R.attr.showDividers};
        public static int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, com.proxy.videowidget.R.attr.actionLayout, com.proxy.videowidget.R.attr.actionProviderClass, com.proxy.videowidget.R.attr.actionViewClass, com.proxy.videowidget.R.attr.alphabeticModifiers, com.proxy.videowidget.R.attr.contentDescription, com.proxy.videowidget.R.attr.iconTint, com.proxy.videowidget.R.attr.iconTintMode, com.proxy.videowidget.R.attr.numericModifiers, com.proxy.videowidget.R.attr.showAsAction, com.proxy.videowidget.R.attr.tooltipText};
        public static int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, com.proxy.videowidget.R.attr.preserveIconSpacing, com.proxy.videowidget.R.attr.subMenuArrow};
        public static int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, com.proxy.videowidget.R.attr.overlapAnchor};
        public static int[] PopupWindowBackgroundState = {com.proxy.videowidget.R.attr.state_above_anchor};
        public static int[] RecycleListView = {com.proxy.videowidget.R.attr.paddingBottomNoButtons, com.proxy.videowidget.R.attr.paddingTopNoTitle};
        public static int[] SearchView = {android.R.attr.textAppearance, android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.text, android.R.attr.hint, android.R.attr.inputType, android.R.attr.imeOptions, com.proxy.videowidget.R.attr.animateMenuItems, com.proxy.videowidget.R.attr.animateNavigationIcon, com.proxy.videowidget.R.attr.autoShowKeyboard, com.proxy.videowidget.R.attr.backHandlingEnabled, com.proxy.videowidget.R.attr.backgroundTint, com.proxy.videowidget.R.attr.closeIcon, com.proxy.videowidget.R.attr.commitIcon, com.proxy.videowidget.R.attr.defaultQueryHint, com.proxy.videowidget.R.attr.goIcon, com.proxy.videowidget.R.attr.headerLayout, com.proxy.videowidget.R.attr.hideNavigationIcon, com.proxy.videowidget.R.attr.iconifiedByDefault, com.proxy.videowidget.R.attr.layout, com.proxy.videowidget.R.attr.queryBackground, com.proxy.videowidget.R.attr.queryHint, com.proxy.videowidget.R.attr.searchHintIcon, com.proxy.videowidget.R.attr.searchIcon, com.proxy.videowidget.R.attr.searchPrefixText, com.proxy.videowidget.R.attr.submitBackground, com.proxy.videowidget.R.attr.suggestionRowLayout, com.proxy.videowidget.R.attr.useDrawerArrowDrawable, com.proxy.videowidget.R.attr.voiceIcon};
        public static int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, com.proxy.videowidget.R.attr.popupTheme};
        public static int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] StateListDrawableItem = {android.R.attr.drawable};
        public static int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, com.proxy.videowidget.R.attr.showText, com.proxy.videowidget.R.attr.splitTrack, com.proxy.videowidget.R.attr.switchMinWidth, com.proxy.videowidget.R.attr.switchPadding, com.proxy.videowidget.R.attr.switchTextAppearance, com.proxy.videowidget.R.attr.thumbTextPadding, com.proxy.videowidget.R.attr.thumbTint, com.proxy.videowidget.R.attr.thumbTintMode, com.proxy.videowidget.R.attr.track, com.proxy.videowidget.R.attr.trackTint, com.proxy.videowidget.R.attr.trackTintMode};
        public static int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, com.proxy.videowidget.R.attr.fontFamily, com.proxy.videowidget.R.attr.fontVariationSettings, com.proxy.videowidget.R.attr.textAllCaps, com.proxy.videowidget.R.attr.textLocale};
        public static int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, com.proxy.videowidget.R.attr.buttonGravity, com.proxy.videowidget.R.attr.collapseContentDescription, com.proxy.videowidget.R.attr.collapseIcon, com.proxy.videowidget.R.attr.contentInsetEnd, com.proxy.videowidget.R.attr.contentInsetEndWithActions, com.proxy.videowidget.R.attr.contentInsetLeft, com.proxy.videowidget.R.attr.contentInsetRight, com.proxy.videowidget.R.attr.contentInsetStart, com.proxy.videowidget.R.attr.contentInsetStartWithNavigation, com.proxy.videowidget.R.attr.logo, com.proxy.videowidget.R.attr.logoDescription, com.proxy.videowidget.R.attr.maxButtonHeight, com.proxy.videowidget.R.attr.menu, com.proxy.videowidget.R.attr.navigationContentDescription, com.proxy.videowidget.R.attr.navigationIcon, com.proxy.videowidget.R.attr.popupTheme, com.proxy.videowidget.R.attr.subtitle, com.proxy.videowidget.R.attr.subtitleTextAppearance, com.proxy.videowidget.R.attr.subtitleTextColor, com.proxy.videowidget.R.attr.title, com.proxy.videowidget.R.attr.titleMargin, com.proxy.videowidget.R.attr.titleMarginBottom, com.proxy.videowidget.R.attr.titleMarginEnd, com.proxy.videowidget.R.attr.titleMarginStart, com.proxy.videowidget.R.attr.titleMarginTop, com.proxy.videowidget.R.attr.titleMargins, com.proxy.videowidget.R.attr.titleTextAppearance, com.proxy.videowidget.R.attr.titleTextColor};
        public static int[] View = {android.R.attr.theme, android.R.attr.focusable, com.proxy.videowidget.R.attr.paddingEnd, com.proxy.videowidget.R.attr.paddingStart, com.proxy.videowidget.R.attr.theme};
        public static int[] ViewBackgroundHelper = {android.R.attr.background, com.proxy.videowidget.R.attr.backgroundTint, com.proxy.videowidget.R.attr.backgroundTintMode};
        public static int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};

        private styleable() {
        }
    }

    private R() {
    }
}
