package com.proxy.videowidget.presentation.base;

import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.os.Build;
import android.os.Bundle;
import android.view.Window;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.accessibility.AccessibilityEventCompat;
import com.proxy.videowidget.domain.util.pref.MyPref;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: BaseActivity.kt */
@Metadata(d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b'\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\b\u0010\u0019\u001a\u00020\u0016H\u0002J\u0012\u0010\u001a\u001a\u00020\u00162\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0014R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086.¢\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u001e\u0010\t\u001a\u00020\n8\u0006@\u0006X\u0087.¢\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR\u001e\u0010\u000f\u001a\u00020\u00108\u0006@\u0006X\u0087.¢\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014¨\u0006\u001d"}, d2 = {"Lcom/proxy/videowidget/presentation/base/BaseActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "mContext", "Landroid/app/Activity;", "getMContext", "()Landroid/app/Activity;", "setMContext", "(Landroid/app/Activity;)V", "myPref", "Lcom/proxy/videowidget/domain/util/pref/MyPref;", "getMyPref", "()Lcom/proxy/videowidget/domain/util/pref/MyPref;", "setMyPref", "(Lcom/proxy/videowidget/domain/util/pref/MyPref;)V", "notificationManager", "Landroid/app/NotificationManager;", "getNotificationManager", "()Landroid/app/NotificationManager;", "setNotificationManager", "(Landroid/app/NotificationManager;)V", "createChannel", "", "isNightMode", "", "makeStatusBarTransparent", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@AndroidEntryPoint
/* loaded from: classes3.dex */
public abstract class BaseActivity extends Hilt_BaseActivity {
    public Activity mContext;

    @Inject
    public MyPref myPref;

    @Inject
    public NotificationManager notificationManager;

    public final Activity getMContext() {
        Activity activity = this.mContext;
        if (activity != null) {
            return activity;
        }
        Intrinsics.throwUninitializedPropertyAccessException("mContext");
        return null;
    }

    public final void setMContext(Activity activity) {
        Intrinsics.checkNotNullParameter(activity, "<set-?>");
        this.mContext = activity;
    }

    public final MyPref getMyPref() {
        MyPref myPref = this.myPref;
        if (myPref != null) {
            return myPref;
        }
        Intrinsics.throwUninitializedPropertyAccessException("myPref");
        return null;
    }

    public final void setMyPref(MyPref myPref) {
        Intrinsics.checkNotNullParameter(myPref, "<set-?>");
        this.myPref = myPref;
    }

    public final NotificationManager getNotificationManager() {
        NotificationManager notificationManager = this.notificationManager;
        if (notificationManager != null) {
            return notificationManager;
        }
        Intrinsics.throwUninitializedPropertyAccessException("notificationManager");
        return null;
    }

    public final void setNotificationManager(NotificationManager notificationManager) {
        Intrinsics.checkNotNullParameter(notificationManager, "<set-?>");
        this.notificationManager = notificationManager;
    }

    @Override // com.proxy.videowidget.presentation.base.Hilt_BaseActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setMContext(this);
        createChannel(getNotificationManager());
        makeStatusBarTransparent();
    }

    private final void createChannel(NotificationManager notificationManager) {
        if (Build.VERSION.SDK_INT >= 26) {
            notificationManager.createNotificationChannel(new NotificationChannel("running_text_channel", "Running Text Channel", 3));
        }
    }

    private final void makeStatusBarTransparent() {
        Window window = getWindow();
        window.requestFeature(1);
        window.clearFlags(AccessibilityEventCompat.TYPE_VIEW_TARGETED_BY_SCROLL);
        window.addFlags(Integer.MIN_VALUE);
        window.getDecorView().setSystemUiVisibility((isNightMode() ? 0 : 8192) | 1024);
        window.setStatusBarColor(0);
    }

    private final boolean isNightMode() {
        return (getResources().getConfiguration().uiMode & 48) == 32;
    }
}
