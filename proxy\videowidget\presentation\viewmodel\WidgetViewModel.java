package com.proxy.videowidget.presentation.viewmodel;

import android.app.Application;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import com.google.android.exoplayer2.util.MimeTypes;
import com.proxy.videowidget.presentation.widget.Widget;
import com.proxy.videowidget.repository.WidgetRepository;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: WidgetViewModel.kt */
@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\u0012\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\n"}, d2 = {"Lcom/proxy/videowidget/presentation/viewmodel/WidgetViewModel;", "Landroidx/lifecycle/AndroidViewModel;", MimeTypes.BASE_TYPE_APPLICATION, "Landroid/app/Application;", "(Landroid/app/Application;)V", "widgets", "Landroidx/lifecycle/LiveData;", "", "Lcom/proxy/videowidget/presentation/widget/Widget;", "getWidgets", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WidgetViewModel extends AndroidViewModel {
    private final LiveData<List<Widget>> widgets;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public WidgetViewModel(Application application) {
        super(application);
        Intrinsics.checkNotNullParameter(application, "application");
        this.widgets = WidgetRepository.INSTANCE.getWidgets();
    }

    public final LiveData<List<Widget>> getWidgets() {
        return this.widgets;
    }
}
