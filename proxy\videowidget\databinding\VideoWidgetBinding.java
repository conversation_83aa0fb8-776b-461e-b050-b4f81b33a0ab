package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class VideoWidgetBinding implements ViewBinding {
    public final ImageView imageVideoView;
    public final ProgressBar progressBar;
    private final RelativeLayout rootView;
    public final RelativeLayout widgetContainer;

    private VideoWidgetBinding(RelativeLayout relativeLayout, ImageView imageView, ProgressBar progressBar, RelativeLayout relativeLayout2) {
        this.rootView = relativeLayout;
        this.imageVideoView = imageView;
        this.progressBar = progressBar;
        this.widgetContainer = relativeLayout2;
    }

    @Override // androidx.viewbinding.ViewBinding
    public RelativeLayout getRoot() {
        return this.rootView;
    }

    public static VideoWidgetBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static VideoWidgetBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.video_widget, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static VideoWidgetBinding bind(View view) {
        int i = R.id.imageVideoView;
        ImageView imageView = (ImageView) ViewBindings.findChildViewById(view, i);
        if (imageView != null) {
            i = R.id.progressBar;
            ProgressBar progressBar = (ProgressBar) ViewBindings.findChildViewById(view, i);
            if (progressBar != null) {
                RelativeLayout relativeLayout = (RelativeLayout) view;
                return new VideoWidgetBinding(relativeLayout, imageView, progressBar, relativeLayout);
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
