package com.proxy.videowidget.presentation.fragments;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Outline;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.fragment.app.FragmentViewModelLazyKt;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.webkit.internal.AssetHelper;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;
import com.proxy.videowidget.presentation.adapter.Notification;
import com.proxy.videowidget.presentation.adapter.WidgetAdapter;
import com.proxy.videowidget.presentation.viewmodel.NotificationViewModel;
import com.proxy.videowidget.presentation.viewmodel.WidgetViewModel;
import com.proxy.videowidget.presentation.widget.Widget;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import kotlin.Lazy;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;

/* compiled from: AccountFragment.kt */
@Metadata(d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\b\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0015\u001a\u00020\u0014H\u0002J\b\u0010\u0016\u001a\u00020\u0014H\u0002J$\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010\u001f\u001a\u00020\u0014H\u0002J\u0010\u0010 \u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u0018H\u0002J\u0010\u0010\"\u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u0018H\u0002J\b\u0010#\u001a\u00020\u0014H\u0002J\b\u0010$\u001a\u00020\u0014H\u0002J\u0010\u0010%\u001a\u00020\u00142\u0006\u0010&\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e¢\u0006\u0002\n\u0000R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.¢\u0006\u0002\n\u0000¨\u0006'"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/AccountFragment;", "Landroidx/fragment/app/Fragment;", "()V", "adapter", "Lcom/proxy/videowidget/presentation/adapter/WidgetAdapter;", "emptyListText", "Landroid/widget/TextView;", "hasNotifications", "", "notificationViewModel", "Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "getNotificationViewModel", "()Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "notificationViewModel$delegate", "Lkotlin/Lazy;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "viewModel", "Lcom/proxy/videowidget/presentation/viewmodel/WidgetViewModel;", "navigateToNotificationFragment", "", "navigateToSettingsFragment", "observeNotifications", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", TtmlNode.RUBY_CONTAINER, "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "rateApp", "setupRecyclerView", "view", "setupUI", "setupViewModel", "shareApp", "showMoreMenu", "anchorView", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class AccountFragment extends Fragment {
    private WidgetAdapter adapter;
    private TextView emptyListText;
    private boolean hasNotifications;

    /* renamed from: notificationViewModel$delegate, reason: from kotlin metadata */
    private final Lazy notificationViewModel;
    private RecyclerView recyclerView;
    private WidgetViewModel viewModel;

    public AccountFragment() {
        final AccountFragment accountFragment = this;
        final Function0 function0 = null;
        this.notificationViewModel = FragmentViewModelLazyKt.createViewModelLazy(accountFragment, Reflection.getOrCreateKotlinClass(NotificationViewModel.class), new Function0<ViewModelStore>() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$special$$inlined$activityViewModels$default$1
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelStore invoke() {
                return accountFragment.requireActivity().getViewModelStore();
            }
        }, new Function0<CreationExtras>() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$special$$inlined$activityViewModels$default$2
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final CreationExtras invoke() {
                CreationExtras creationExtras;
                Function0 function02 = function0;
                return (function02 == null || (creationExtras = (CreationExtras) function02.invoke()) == null) ? accountFragment.requireActivity().getDefaultViewModelCreationExtras() : creationExtras;
            }
        }, new Function0<ViewModelProvider.Factory>() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$special$$inlined$activityViewModels$default$3
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelProvider.Factory invoke() {
                return accountFragment.requireActivity().getDefaultViewModelProviderFactory();
            }
        });
    }

    private final NotificationViewModel getNotificationViewModel() {
        return (NotificationViewModel) this.notificationViewModel.getValue();
    }

    @Override // androidx.fragment.app.Fragment
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Intrinsics.checkNotNullParameter(inflater, "inflater");
        View viewInflate = inflater.inflate(R.layout.fragment_account, container, false);
        View viewFindViewById = viewInflate.findViewById(R.id.emptyListText);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        TextView textView = (TextView) viewFindViewById;
        this.emptyListText = textView;
        if (textView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("emptyListText");
            textView = null;
        }
        textView.setVisibility(8);
        Intrinsics.checkNotNull(viewInflate);
        setupRecyclerView(viewInflate);
        setupViewModel();
        observeNotifications();
        setupUI(viewInflate);
        return viewInflate;
    }

    private final void setupRecyclerView(View view) {
        View viewFindViewById = view.findViewById(R.id.recyclerViewWidgets);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        RecyclerView recyclerView = (RecyclerView) viewFindViewById;
        this.recyclerView = recyclerView;
        RecyclerView recyclerView2 = null;
        if (recyclerView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
            recyclerView = null;
        }
        recyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        RecyclerView recyclerView3 = this.recyclerView;
        if (recyclerView3 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
            recyclerView3 = null;
        }
        recyclerView3.setClipChildren(false);
        RecyclerView recyclerView4 = this.recyclerView;
        if (recyclerView4 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
            recyclerView4 = null;
        }
        recyclerView4.setClipToPadding(false);
        this.adapter = new WidgetAdapter(new ArrayList());
        RecyclerView recyclerView5 = this.recyclerView;
        if (recyclerView5 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
            recyclerView5 = null;
        }
        WidgetAdapter widgetAdapter = this.adapter;
        if (widgetAdapter == null) {
            Intrinsics.throwUninitializedPropertyAccessException("adapter");
            widgetAdapter = null;
        }
        recyclerView5.setAdapter(widgetAdapter);
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new ItemTouchHelper.SimpleCallback() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$setupRecyclerView$itemTouchHelperCallback$1
            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {
                Intrinsics.checkNotNullParameter(viewHolder, "viewHolder");
            }

            {
                super(15, 0);
            }

            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            public boolean onMove(RecyclerView recyclerView6, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
                Intrinsics.checkNotNullParameter(recyclerView6, "recyclerView");
                Intrinsics.checkNotNullParameter(viewHolder, "viewHolder");
                Intrinsics.checkNotNullParameter(target, "target");
                int adapterPosition = viewHolder.getAdapterPosition();
                int adapterPosition2 = target.getAdapterPosition();
                WidgetAdapter widgetAdapter2 = this.this$0.adapter;
                if (widgetAdapter2 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("adapter");
                    widgetAdapter2 = null;
                }
                widgetAdapter2.swapItems(adapterPosition, adapterPosition2);
                return true;
            }

            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            public void onChildDraw(Canvas c, RecyclerView recyclerView6, RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
                Intrinsics.checkNotNullParameter(c, "c");
                Intrinsics.checkNotNullParameter(recyclerView6, "recyclerView");
                Intrinsics.checkNotNullParameter(viewHolder, "viewHolder");
                View itemView = viewHolder.itemView;
                Intrinsics.checkNotNullExpressionValue(itemView, "itemView");
                itemView.setTranslationX(dX);
                itemView.setTranslationY(dY);
                super.onChildDraw(c, recyclerView6, viewHolder, dX, dY, actionState, isCurrentlyActive);
            }
        });
        RecyclerView recyclerView6 = this.recyclerView;
        if (recyclerView6 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
        } else {
            recyclerView2 = recyclerView6;
        }
        itemTouchHelper.attachToRecyclerView(recyclerView2);
    }

    private final void setupViewModel() {
        WidgetViewModel widgetViewModel = (WidgetViewModel) new ViewModelProvider(this).get(WidgetViewModel.class);
        this.viewModel = widgetViewModel;
        TextView textView = null;
        if (widgetViewModel == null) {
            Intrinsics.throwUninitializedPropertyAccessException("viewModel");
            widgetViewModel = null;
        }
        widgetViewModel.getWidgets().observe(getViewLifecycleOwner(), new AccountFragment$sam$androidx_lifecycle_Observer$0(new Function1<List<? extends Widget>, Unit>() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment.setupViewModel.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(List<? extends Widget> list) {
                invoke2((List<Widget>) list);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(List<Widget> list) {
                WidgetAdapter widgetAdapter = AccountFragment.this.adapter;
                TextView textView2 = null;
                if (widgetAdapter == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("adapter");
                    widgetAdapter = null;
                }
                Intrinsics.checkNotNull(list);
                widgetAdapter.updateData(CollectionsKt.toMutableList((Collection) list));
                TextView textView3 = AccountFragment.this.emptyListText;
                if (textView3 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("emptyListText");
                } else {
                    textView2 = textView3;
                }
                textView2.setVisibility(list.isEmpty() ? 0 : 8);
            }
        }));
        WidgetViewModel widgetViewModel2 = this.viewModel;
        if (widgetViewModel2 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("viewModel");
            widgetViewModel2 = null;
        }
        List<Widget> value = widgetViewModel2.getWidgets().getValue();
        TextView textView2 = this.emptyListText;
        if (textView2 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("emptyListText");
        } else {
            textView = textView2;
        }
        textView.setVisibility((value == null || !value.isEmpty()) ? 8 : 0);
    }

    private final void setupUI(View view) {
        View viewFindViewById = view.findViewById(R.id.moreBtn);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        ((MaterialButton) viewFindViewById).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda4
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) throws Resources.NotFoundException {
                AccountFragment.setupUI$lambda$0(this.f$0, view2);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupUI$lambda$0(AccountFragment this$0, View view) throws Resources.NotFoundException {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNull(view);
        this$0.showMoreMenu(view);
    }

    private final void observeNotifications() {
        getNotificationViewModel().getNotifications().observe(getViewLifecycleOwner(), new AccountFragment$sam$androidx_lifecycle_Observer$0(new Function1<List<? extends Notification>, Unit>() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment.observeNotifications.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(List<? extends Notification> list) {
                invoke2((List<Notification>) list);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(List<Notification> list) {
                AccountFragment accountFragment = AccountFragment.this;
                Intrinsics.checkNotNull(list);
                accountFragment.hasNotifications = !list.isEmpty();
            }
        }));
    }

    private final void showMoreMenu(View anchorView) throws Resources.NotFoundException {
        View viewInflate = LayoutInflater.from(getContext()).inflate(R.layout.popup_menu, (ViewGroup) null);
        final PopupWindow popupWindow = new PopupWindow(viewInflate, -2, -2, true);
        final float dimension = getResources().getDimension(R.dimen.corner_radius_16dp);
        viewInflate.setOutlineProvider(new ViewOutlineProvider() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment.showMoreMenu.1
            @Override // android.view.ViewOutlineProvider
            public void getOutline(View view, Outline outline) {
                Intrinsics.checkNotNullParameter(view, "view");
                Intrinsics.checkNotNullParameter(outline, "outline");
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), dimension);
            }
        });
        viewInflate.setClipToOutline(true);
        TextView textView = (TextView) viewInflate.findViewById(R.id.action_notifications);
        textView.setVisibility(this.hasNotifications ? 0 : 8);
        textView.setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                AccountFragment.showMoreMenu$lambda$1(this.f$0, popupWindow, view);
            }
        });
        ((TextView) viewInflate.findViewById(R.id.action_manage_subscription)).setVisibility(8);
        ((TextView) viewInflate.findViewById(R.id.action_rate)).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                AccountFragment.showMoreMenu$lambda$2(this.f$0, popupWindow, view);
            }
        });
        ((TextView) viewInflate.findViewById(R.id.action_share)).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda2
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                AccountFragment.showMoreMenu$lambda$3(this.f$0, popupWindow, view);
            }
        });
        ((TextView) viewInflate.findViewById(R.id.action_settings)).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda3
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                AccountFragment.showMoreMenu$lambda$4(this.f$0, popupWindow, view);
            }
        });
        popupWindow.showAsDropDown(anchorView, 0, 0);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showMoreMenu$lambda$1(AccountFragment this$0, PopupWindow popupWindow, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(popupWindow, "$popupWindow");
        this$0.navigateToNotificationFragment();
        popupWindow.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showMoreMenu$lambda$2(AccountFragment this$0, PopupWindow popupWindow, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(popupWindow, "$popupWindow");
        this$0.rateApp();
        popupWindow.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showMoreMenu$lambda$3(AccountFragment this$0, PopupWindow popupWindow, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(popupWindow, "$popupWindow");
        this$0.shareApp();
        popupWindow.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showMoreMenu$lambda$4(AccountFragment this$0, PopupWindow popupWindow, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(popupWindow, "$popupWindow");
        this$0.navigateToSettingsFragment();
        popupWindow.dismiss();
    }

    private final void navigateToNotificationFragment() {
        final NotificationFragment notificationFragmentNewInstance = NotificationFragment.INSTANCE.newInstance();
        View view = getView();
        if (view != null) {
            view.post(new Runnable() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda6
                @Override // java.lang.Runnable
                public final void run() {
                    AccountFragment.navigateToNotificationFragment$lambda$5(this.f$0, notificationFragmentNewInstance);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void navigateToNotificationFragment$lambda$5(AccountFragment this$0, NotificationFragment fragment) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(fragment, "$fragment");
        this$0.getParentFragmentManager().beginTransaction().replace(R.id.fragment_container, fragment).addToBackStack("NotificationFragment").setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE).commitAllowingStateLoss();
    }

    private final void navigateToSettingsFragment() {
        final SettingsFragment settingsFragmentNewInstance = SettingsFragment.INSTANCE.newInstance();
        View view = getView();
        if (view != null) {
            view.post(new Runnable() { // from class: com.proxy.videowidget.presentation.fragments.AccountFragment$$ExternalSyntheticLambda5
                @Override // java.lang.Runnable
                public final void run() {
                    AccountFragment.navigateToSettingsFragment$lambda$6(this.f$0, settingsFragmentNewInstance);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void navigateToSettingsFragment$lambda$6(AccountFragment this$0, SettingsFragment fragment) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(fragment, "$fragment");
        this$0.getParentFragmentManager().beginTransaction().replace(R.id.fragment_container, fragment).addToBackStack("SettingsFragment").setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE).commitAllowingStateLoss();
    }

    private final void rateApp() {
        Context context = getContext();
        if (context != null) {
            try {
                startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + context.getPackageName())));
            } catch (Exception unused) {
                startActivity(new Intent("android.intent.action.VIEW", Uri.parse("https://play.google.com/store/apps/details?id=" + context.getPackageName())));
            }
        }
    }

    private final void shareApp() {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.SEND");
        intent.putExtra("android.intent.extra.TEXT", "Check out this amazing widget app: \nhttps://play.google.com/store/apps/details?id=" + requireContext().getPackageName());
        intent.setType(AssetHelper.DEFAULT_MIME_TYPE);
        startActivity(Intent.createChooser(intent, "Share app via"));
    }
}
