package com.proxy.videowidget.domain.util;

import androidx.constraintlayout.widget.ConstraintLayout;
import com.bumptech.glide.module.AppGlideModule;
import kotlin.Metadata;

/* compiled from: GlideModule.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002¨\u0006\u0003"}, d2 = {"Lcom/proxy/videowidget/domain/util/GlideModule;", "Lcom/bumptech/glide/module/AppGlideModule;", "()V", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class GlideModule extends AppGlideModule {
}
