package com.proxy.videowidget.presentation.fragments;

import android.os.Bundle;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import com.airbnb.lottie.LottieAnimationView;
import com.proxy.videowidget.R;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: InstructionFragment.kt */
@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\b\u0010\u0005\u001a\u00020\u0006H\u0016J\b\u0010\u0007\u001a\u00020\u0006H\u0016J\u001a\u0010\b\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0016R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.¢\u0006\u0002\n\u0000¨\u0006\r"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/InstructionFragment;", "Landroidx/fragment/app/Fragment;", "()V", "lottieView", "Lcom/airbnb/lottie/LottieAnimationView;", "onPause", "", "onResume", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class InstructionFragment extends Fragment {
    private LottieAnimationView lottieView;

    public InstructionFragment() {
        super(R.layout.fragment_instruction);
    }

    @Override // androidx.fragment.app.Fragment
    public void onViewCreated(View view, Bundle savedInstanceState) {
        int i;
        Intrinsics.checkNotNullParameter(view, "view");
        super.onViewCreated(view, savedInstanceState);
        View viewFindViewById = view.findViewById(R.id.lottieView);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        this.lottieView = (LottieAnimationView) viewFindViewById;
        int i2 = getResources().getConfiguration().uiMode & 48;
        if (i2 != 16 && i2 == 32) {
            i = R.raw.instruction_video_dark;
        } else {
            i = R.raw.instruction_video_light;
        }
        LottieAnimationView lottieAnimationView = this.lottieView;
        LottieAnimationView lottieAnimationView2 = null;
        if (lottieAnimationView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("lottieView");
            lottieAnimationView = null;
        }
        lottieAnimationView.setAnimation(i);
        LottieAnimationView lottieAnimationView3 = this.lottieView;
        if (lottieAnimationView3 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("lottieView");
        } else {
            lottieAnimationView2 = lottieAnimationView3;
        }
        lottieAnimationView2.playAnimation();
    }

    @Override // androidx.fragment.app.Fragment
    public void onResume() {
        super.onResume();
        LottieAnimationView lottieAnimationView = this.lottieView;
        if (lottieAnimationView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("lottieView");
            lottieAnimationView = null;
        }
        lottieAnimationView.resumeAnimation();
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        LottieAnimationView lottieAnimationView = this.lottieView;
        if (lottieAnimationView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("lottieView");
            lottieAnimationView = null;
        }
        lottieAnimationView.pauseAnimation();
        super.onPause();
    }
}
