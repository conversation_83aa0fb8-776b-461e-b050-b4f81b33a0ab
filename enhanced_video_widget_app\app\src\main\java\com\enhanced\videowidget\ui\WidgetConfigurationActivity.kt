package com.enhanced.videowidget.ui

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.databinding.ActivityWidgetConfigurationBinding
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.launch

/**
 * Configuration activity for setting up new video widgets
 */
class WidgetConfigurationActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "WidgetConfiguration"
    }
    
    private lateinit var binding: ActivityWidgetConfigurationBinding
    private lateinit var repository: WidgetRepository
    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID
    private var selectedVideoUri: Uri? = null
    private var selectedVideoPath: String? = null
    
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleVideoSelection(uri)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWidgetConfigurationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Set the result to CANCELED initially
        setResult(RESULT_CANCELED)
        
        // Get the widget ID from the intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID
        
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }
        
        setupRepository()
        setupUI()
    }
    
    private fun setupRepository() {
        repository = WidgetRepository.getInstance()
        repository.initialize(this)
    }
    
    private fun setupUI() {
        binding.apply {
            // Set up toolbar
            setSupportActionBar(toolbar)
            supportActionBar?.apply {
                title = getString(R.string.widget_configuration_title)
                setDisplayHomeAsUpEnabled(true)
            }
            
            // Set up video selection button
            buttonSelectVideo.setOnClickListener {
                openVideoPicker()
            }
            
            // Set up create widget button
            buttonCreateWidget.setOnClickListener {
                createWidget()
            }
            
            // Set up cancel button
            buttonCancel.setOnClickListener {
                finish()
            }
            
            // Initially disable create button
            buttonCreateWidget.isEnabled = false
        }
    }
    
    private fun openVideoPicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI).apply {
            type = "video/*"
        }
        videoPickerLauncher.launch(intent)
    }
    
    private fun handleVideoSelection(videoUri: Uri) {
        Log.d(TAG, "Video selected: $videoUri")
        
        selectedVideoUri = videoUri
        selectedVideoPath = getVideoPath(videoUri)
        
        if (selectedVideoPath != null) {
            binding.apply {
                textViewSelectedVideo.text = getString(R.string.video_selected)
                buttonCreateWidget.isEnabled = true
            }
            
            // Show video preview if possible
            lifecycleScope.launch {
                try {
                    val frameExtractor = VideoFrameExtractor()
                    val metadata = frameExtractor.getVideoMetadata(selectedVideoPath!!)
                    
                    metadata?.let {
                        val previewFrame = frameExtractor.extractFrameAtTime(selectedVideoPath!!, 0)
                        previewFrame?.let { bitmap ->
                            binding.imageViewPreview.setImageBitmap(bitmap)
                            binding.imageViewPreview.visibility = android.view.View.VISIBLE
                        }
                        
                        binding.textViewVideoInfo.text = "Duration: ${it.duration / 1000}s, ${it.width}x${it.height}"
                        binding.textViewVideoInfo.visibility = android.view.View.VISIBLE
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error extracting video preview", e)
                }
            }
        } else {
            Toast.makeText(this, getString(R.string.error_file_not_found), Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun getVideoPath(uri: Uri): String? {
        val projection = arrayOf(MediaStore.Video.Media.DATA)
        contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                return cursor.getString(columnIndex)
            }
        }
        return null
    }
    
    private fun createWidget() {
        val videoPath = selectedVideoPath
        if (videoPath == null) {
            Toast.makeText(this, getString(R.string.no_video_selected), Toast.LENGTH_SHORT).show()
            return
        }
        
        lifecycleScope.launch {
            try {
                // Show loading
                binding.apply {
                    progressBar.visibility = android.view.View.VISIBLE
                    buttonCreateWidget.isEnabled = false
                    buttonSelectVideo.isEnabled = false
                }
                
                // Extract video metadata
                val frameExtractor = VideoFrameExtractor()
                val metadata = frameExtractor.getVideoMetadata(videoPath)
                
                if (metadata == null) {
                    showError(getString(R.string.error_video_processing))
                    return@launch
                }
                
                // Get widget name from input or use default
                val widgetName = binding.editTextWidgetName.text.toString().ifEmpty {
                    "Video Widget $appWidgetId"
                }
                
                // Create widget
                val widget = Widget(
                    id = appWidgetId,
                    name = widgetName,
                    description = "Duration: ${metadata.duration / 1000}s",
                    videoPath = videoPath,
                    framerate = metadata.frameRate,
                    mediaType = "video"
                )
                
                // Add widget to repository
                repository.addOrUpdateWidget(widget)
                
                // Update the widget
                val appWidgetManager = AppWidgetManager.getInstance(this@WidgetConfigurationActivity)
                EnhancedVideoWidget.updateAppWidget(
                    this@WidgetConfigurationActivity,
                    appWidgetManager,
                    appWidgetId,
                    widget
                )
                
                // Set result and finish
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(RESULT_OK, resultValue)
                
                Toast.makeText(this@WidgetConfigurationActivity, getString(R.string.widget_created), Toast.LENGTH_SHORT).show()
                finish()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating widget", e)
                showError(getString(R.string.error_widget_creation))
            } finally {
                // Hide loading
                binding.apply {
                    progressBar.visibility = android.view.View.GONE
                    buttonCreateWidget.isEnabled = true
                    buttonSelectVideo.isEnabled = true
                }
            }
        }
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
