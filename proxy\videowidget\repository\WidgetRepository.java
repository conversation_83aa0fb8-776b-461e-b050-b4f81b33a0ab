package com.proxy.videowidget.repository;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.proxy.videowidget.presentation.widget.Widget;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.io.CloseableKt;
import kotlin.io.FilesKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;
import kotlin.text.StringsKt;

/* compiled from: WidgetRepository.kt */
@Metadata(d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\bÆ\u0002\u0018\u00002\u00020\u0001:\u0001*B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\tJ\b\u0010\u0015\u001a\u00020\u0013H\u0002J\u0006\u0010\u0016\u001a\u00020\u0013J\u0010\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0002J\u0018\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u000e\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u001e\u001a\u00020\u000bJ\u0012\u0010\u001f\u001a\u0004\u0018\u00010 2\u0006\u0010\u0018\u001a\u00020\u0019H\u0002J\u0010\u0010!\u001a\u0004\u0018\u00010 2\u0006\u0010\u0018\u001a\u00020\u0019J\b\u0010\"\u001a\u00020\u0013H\u0002J\u0018\u0010#\u001a\u00020\u00132\u0006\u0010$\u001a\u00020\u00042\b\u0010%\u001a\u0004\u0018\u00010\u0004J\u000e\u0010&\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\tJ\"\u0010'\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u00192\b\u0010(\u001a\u0004\u0018\u00010 2\b\b\u0002\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010)\u001a\u00020\u00132\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004¢\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u000f8F¢\u0006\u0006\u001a\u0004\b\u0010\u0010\u0011¨\u0006+"}, d2 = {"Lcom/proxy/videowidget/repository/WidgetRepository;", "", "()V", "TRACK_FILE_NAME", "", "WIDGETS_FILE_NAME", "_widgets", "Landroidx/lifecycle/MutableLiveData;", "", "Lcom/proxy/videowidget/presentation/widget/Widget;", "appContext", "Landroid/content/Context;", "fileOperationLock", "Ljava/util/concurrent/locks/ReentrantLock;", "widgets", "Landroidx/lifecycle/LiveData;", "getWidgets", "()Landroidx/lifecycle/LiveData;", "addOrUpdateWidget", "", "widget", "cleanCache", "cleanUserData", "deleteBitmapFile", TtmlNode.ATTR_ID, "", "getImageFilePath", "isImageWidget", "", "initialize", "context", "loadBitmapFromFile", "Landroid/graphics/Bitmap;", "loadImageBitmapFromFile", "loadWidgetsFromStorage", "logAction", "action", "filePath", "removeWidget", "saveBitmapToFile", "bitmap", "saveWidgetsToStorage", "TrackEntry", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WidgetRepository {
    private static final String TRACK_FILE_NAME = "file_track_log.json";
    private static final String WIDGETS_FILE_NAME = "widgets.json";
    private static Context appContext;
    public static final WidgetRepository INSTANCE = new WidgetRepository();
    private static final MutableLiveData<List<Widget>> _widgets = new MutableLiveData<>();
    private static final ReentrantLock fileOperationLock = new ReentrantLock();

    private WidgetRepository() {
    }

    public final LiveData<List<Widget>> getWidgets() {
        return _widgets;
    }

    public final void initialize(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        appContext = context.getApplicationContext();
        loadWidgetsFromStorage();
        cleanUserData();
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0025 A[Catch: all -> 0x0090, TryCatch #0 {all -> 0x0090, blocks: (B:3:0x000c, B:5:0x0010, B:7:0x001a, B:10:0x002c, B:11:0x0031, B:13:0x0038, B:19:0x004f, B:21:0x0056, B:20:0x0053, B:16:0x0049, B:9:0x0025, B:24:0x0088, B:25:0x008f), top: B:29:0x000c }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void addOrUpdateWidget(com.proxy.videowidget.presentation.widget.Widget r8) {
        /*
            r7 = this;
            java.lang.String r0 = "widget"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r8, r0)
            java.util.concurrent.locks.ReentrantLock r0 = com.proxy.videowidget.repository.WidgetRepository.fileOperationLock
            java.util.concurrent.locks.Lock r0 = (java.util.concurrent.locks.Lock) r0
            r0.lock()
            android.content.Context r1 = com.proxy.videowidget.repository.WidgetRepository.appContext     // Catch: java.lang.Throwable -> L90
            if (r1 == 0) goto L88
            androidx.lifecycle.MutableLiveData<java.util.List<com.proxy.videowidget.presentation.widget.Widget>> r1 = com.proxy.videowidget.repository.WidgetRepository._widgets     // Catch: java.lang.Throwable -> L90
            java.lang.Object r1 = r1.getValue()     // Catch: java.lang.Throwable -> L90
            java.util.List r1 = (java.util.List) r1     // Catch: java.lang.Throwable -> L90
            if (r1 == 0) goto L25
            kotlin.jvm.internal.Intrinsics.checkNotNull(r1)     // Catch: java.lang.Throwable -> L90
            java.util.Collection r1 = (java.util.Collection) r1     // Catch: java.lang.Throwable -> L90
            java.util.List r1 = kotlin.collections.CollectionsKt.toMutableList(r1)     // Catch: java.lang.Throwable -> L90
            if (r1 != 0) goto L2c
        L25:
            java.util.ArrayList r1 = new java.util.ArrayList     // Catch: java.lang.Throwable -> L90
            r1.<init>()     // Catch: java.lang.Throwable -> L90
            java.util.List r1 = (java.util.List) r1     // Catch: java.lang.Throwable -> L90
        L2c:
            java.util.Iterator r2 = r1.iterator()     // Catch: java.lang.Throwable -> L90
            r3 = 0
        L31:
            boolean r4 = r2.hasNext()     // Catch: java.lang.Throwable -> L90
            r5 = -1
            if (r4 == 0) goto L4c
            java.lang.Object r4 = r2.next()     // Catch: java.lang.Throwable -> L90
            com.proxy.videowidget.presentation.widget.Widget r4 = (com.proxy.videowidget.presentation.widget.Widget) r4     // Catch: java.lang.Throwable -> L90
            int r4 = r4.getId()     // Catch: java.lang.Throwable -> L90
            int r6 = r8.getId()     // Catch: java.lang.Throwable -> L90
            if (r4 != r6) goto L49
            goto L4d
        L49:
            int r3 = r3 + 1
            goto L31
        L4c:
            r3 = r5
        L4d:
            if (r3 == r5) goto L53
            r1.set(r3, r8)     // Catch: java.lang.Throwable -> L90
            goto L56
        L53:
            r1.add(r8)     // Catch: java.lang.Throwable -> L90
        L56:
            androidx.lifecycle.MutableLiveData<java.util.List<com.proxy.videowidget.presentation.widget.Widget>> r2 = com.proxy.videowidget.repository.WidgetRepository._widgets     // Catch: java.lang.Throwable -> L90
            r2.postValue(r1)     // Catch: java.lang.Throwable -> L90
            com.proxy.videowidget.repository.WidgetRepository r2 = com.proxy.videowidget.repository.WidgetRepository.INSTANCE     // Catch: java.lang.Throwable -> L90
            r2.saveWidgetsToStorage(r1)     // Catch: java.lang.Throwable -> L90
            java.lang.String r1 = r8.getMediaType()     // Catch: java.lang.Throwable -> L90
            java.lang.String r3 = "image"
            boolean r1 = kotlin.jvm.internal.Intrinsics.areEqual(r1, r3)     // Catch: java.lang.Throwable -> L90
            int r3 = r8.getId()     // Catch: java.lang.Throwable -> L90
            android.graphics.Bitmap r4 = r8.getFirstFrame()     // Catch: java.lang.Throwable -> L90
            r2.saveBitmapToFile(r3, r4, r1)     // Catch: java.lang.Throwable -> L90
            java.lang.String r3 = "saved"
            int r8 = r8.getId()     // Catch: java.lang.Throwable -> L90
            java.lang.String r8 = r2.getImageFilePath(r8, r1)     // Catch: java.lang.Throwable -> L90
            r2.logAction(r3, r8)     // Catch: java.lang.Throwable -> L90
            kotlin.Unit r8 = kotlin.Unit.INSTANCE     // Catch: java.lang.Throwable -> L90
            r0.unlock()
            return
        L88:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException     // Catch: java.lang.Throwable -> L90
            java.lang.String r1 = "WidgetRepository is not initialized."
            r8.<init>(r1)     // Catch: java.lang.Throwable -> L90
            throw r8     // Catch: java.lang.Throwable -> L90
        L90:
            r8 = move-exception
            r0.unlock()
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.repository.WidgetRepository.addOrUpdateWidget(com.proxy.videowidget.presentation.widget.Widget):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0025 A[Catch: all -> 0x006a, TryCatch #0 {all -> 0x006a, blocks: (B:3:0x000c, B:5:0x0010, B:7:0x001a, B:10:0x002c, B:9:0x0025, B:13:0x0062, B:14:0x0069), top: B:18:0x000c }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void removeWidget(final com.proxy.videowidget.presentation.widget.Widget r5) {
        /*
            r4 = this;
            java.lang.String r0 = "widget"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r5, r0)
            java.util.concurrent.locks.ReentrantLock r0 = com.proxy.videowidget.repository.WidgetRepository.fileOperationLock
            java.util.concurrent.locks.Lock r0 = (java.util.concurrent.locks.Lock) r0
            r0.lock()
            android.content.Context r1 = com.proxy.videowidget.repository.WidgetRepository.appContext     // Catch: java.lang.Throwable -> L6a
            if (r1 == 0) goto L62
            androidx.lifecycle.MutableLiveData<java.util.List<com.proxy.videowidget.presentation.widget.Widget>> r1 = com.proxy.videowidget.repository.WidgetRepository._widgets     // Catch: java.lang.Throwable -> L6a
            java.lang.Object r2 = r1.getValue()     // Catch: java.lang.Throwable -> L6a
            java.util.List r2 = (java.util.List) r2     // Catch: java.lang.Throwable -> L6a
            if (r2 == 0) goto L25
            kotlin.jvm.internal.Intrinsics.checkNotNull(r2)     // Catch: java.lang.Throwable -> L6a
            java.util.Collection r2 = (java.util.Collection) r2     // Catch: java.lang.Throwable -> L6a
            java.util.List r2 = kotlin.collections.CollectionsKt.toMutableList(r2)     // Catch: java.lang.Throwable -> L6a
            if (r2 != 0) goto L2c
        L25:
            java.util.ArrayList r2 = new java.util.ArrayList     // Catch: java.lang.Throwable -> L6a
            r2.<init>()     // Catch: java.lang.Throwable -> L6a
            java.util.List r2 = (java.util.List) r2     // Catch: java.lang.Throwable -> L6a
        L2c:
            com.proxy.videowidget.repository.WidgetRepository$removeWidget$1$1 r3 = new com.proxy.videowidget.repository.WidgetRepository$removeWidget$1$1     // Catch: java.lang.Throwable -> L6a
            r3.<init>()     // Catch: java.lang.Throwable -> L6a
            kotlin.jvm.functions.Function1 r3 = (kotlin.jvm.functions.Function1) r3     // Catch: java.lang.Throwable -> L6a
            kotlin.collections.CollectionsKt.removeAll(r2, r3)     // Catch: java.lang.Throwable -> L6a
            r1.postValue(r2)     // Catch: java.lang.Throwable -> L6a
            com.proxy.videowidget.repository.WidgetRepository r1 = com.proxy.videowidget.repository.WidgetRepository.INSTANCE     // Catch: java.lang.Throwable -> L6a
            r1.saveWidgetsToStorage(r2)     // Catch: java.lang.Throwable -> L6a
            int r2 = r5.getId()     // Catch: java.lang.Throwable -> L6a
            r1.deleteBitmapFile(r2)     // Catch: java.lang.Throwable -> L6a
            java.lang.String r2 = r5.getMediaType()     // Catch: java.lang.Throwable -> L6a
            java.lang.String r3 = "image"
            boolean r2 = kotlin.jvm.internal.Intrinsics.areEqual(r2, r3)     // Catch: java.lang.Throwable -> L6a
            java.lang.String r3 = "deleted"
            int r5 = r5.getId()     // Catch: java.lang.Throwable -> L6a
            java.lang.String r5 = r1.getImageFilePath(r5, r2)     // Catch: java.lang.Throwable -> L6a
            r1.logAction(r3, r5)     // Catch: java.lang.Throwable -> L6a
            kotlin.Unit r5 = kotlin.Unit.INSTANCE     // Catch: java.lang.Throwable -> L6a
            r0.unlock()
            return
        L62:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException     // Catch: java.lang.Throwable -> L6a
            java.lang.String r1 = "WidgetRepository is not initialized."
            r5.<init>(r1)     // Catch: java.lang.Throwable -> L6a
            throw r5     // Catch: java.lang.Throwable -> L6a
        L6a:
            r5 = move-exception
            r0.unlock()
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.repository.WidgetRepository.removeWidget(com.proxy.videowidget.presentation.widget.Widget):void");
    }

    private final void saveWidgetsToStorage(List<Widget> widgets) {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File file = new File(context.getFilesDir(), WIDGETS_FILE_NAME);
            Gson gson = new Gson();
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            try {
                String json = gson.toJson(widgets);
                Intrinsics.checkNotNullExpressionValue(json, "toJson(...)");
                byte[] bytes = json.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes, "getBytes(...)");
                fileOutputStream.write(bytes);
                Unit unit = Unit.INSTANCE;
                CloseableKt.closeFinally(fileOutputStream, null);
                Unit unit2 = Unit.INSTANCE;
            } finally {
            }
        } finally {
            reentrantLock.unlock();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:33:0x00ac  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final void loadWidgetsFromStorage() {
        /*
            Method dump skipped, instructions count: 339
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.repository.WidgetRepository.loadWidgetsFromStorage():void");
    }

    public static /* synthetic */ void saveBitmapToFile$default(WidgetRepository widgetRepository, int i, Bitmap bitmap, boolean z, int i2, Object obj) {
        if ((i2 & 4) != 0) {
            z = false;
        }
        widgetRepository.saveBitmapToFile(i, bitmap, z);
    }

    public final void saveBitmapToFile(int id, Bitmap bitmap, boolean isImageWidget) {
        if (bitmap == null) {
            return;
        }
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File file = new File(context.getExternalFilesDir(null), isImageWidget ? "media_" + id + ".png" : "widget_" + id + ".png");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            try {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream);
                CloseableKt.closeFinally(fileOutputStream, null);
                INSTANCE.logAction("saved", file.getAbsolutePath());
                Unit unit = Unit.INSTANCE;
            } finally {
            }
        } finally {
            reentrantLock.unlock();
        }
    }

    private final Bitmap loadBitmapFromFile(int id) {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File file = new File(context.getExternalFilesDir(null), "widget_" + id + ".png");
            return file.exists() ? BitmapFactory.decodeFile(file.getAbsolutePath()) : null;
        } finally {
            reentrantLock.unlock();
        }
    }

    public final Bitmap loadImageBitmapFromFile(int id) {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File file = new File(context.getExternalFilesDir(null), "media_" + id + ".png");
            return file.exists() ? BitmapFactory.decodeFile(file.getAbsolutePath()) : null;
        } finally {
            reentrantLock.unlock();
        }
    }

    private final void deleteBitmapFile(int id) {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File externalFilesDir = context.getExternalFilesDir(null);
            Iterator it = CollectionsKt.listOf((Object[]) new String[]{"widget_" + id + ".png", "media_" + id + ".png"}).iterator();
            while (it.hasNext()) {
                File file = new File(externalFilesDir, (String) it.next());
                if (file.exists()) {
                    file.delete();
                }
            }
            Unit unit = Unit.INSTANCE;
        } finally {
            reentrantLock.unlock();
        }
    }

    private final String getImageFilePath(int id, boolean isImageWidget) {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        String absolutePath = new File(context.getExternalFilesDir(null), isImageWidget ? "media_" + id + ".png" : "widget_" + id + ".png").getAbsolutePath();
        Intrinsics.checkNotNullExpressionValue(absolutePath, "getAbsolutePath(...)");
        return absolutePath;
    }

    public final void logAction(String action, String filePath) {
        ArrayList arrayList;
        FileOutputStream fileOutputStream;
        Intrinsics.checkNotNullParameter(action, "action");
        if (filePath == null) {
            return;
        }
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        ReentrantLock reentrantLock = fileOperationLock;
        reentrantLock.lock();
        try {
            File file = new File(context.getFilesDir(), TRACK_FILE_NAME);
            if (file.exists()) {
                Gson gson = new Gson();
                fileOutputStream = new FileInputStream(file);
                try {
                    arrayList = (List) gson.fromJson(new InputStreamReader(fileOutputStream, Charsets.UTF_8), new TypeToken<List<TrackEntry>>() { // from class: com.proxy.videowidget.repository.WidgetRepository$logAction$1$logEntries$1$listType$1
                    }.getType());
                    if (arrayList == null) {
                        arrayList = new ArrayList();
                    }
                    CloseableKt.closeFinally(fileOutputStream, null);
                } finally {
                }
            } else {
                arrayList = new ArrayList();
            }
            arrayList.add(new TrackEntry(action, filePath, System.currentTimeMillis()));
            Gson gson2 = new Gson();
            fileOutputStream = new FileOutputStream(file);
            try {
                String json = gson2.toJson(arrayList);
                Intrinsics.checkNotNullExpressionValue(json, "toJson(...)");
                byte[] bytes = json.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes, "getBytes(...)");
                fileOutputStream.write(bytes);
                Unit unit = Unit.INSTANCE;
                CloseableKt.closeFinally(fileOutputStream, null);
                Unit unit2 = Unit.INSTANCE;
            } finally {
                try {
                    throw th;
                } finally {
                }
            }
        } finally {
            reentrantLock.unlock();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* compiled from: WidgetRepository.kt */
    @Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006¢\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003HÆ\u0003J\t\u0010\u000e\u001a\u00020\u0003HÆ\u0003J\t\u0010\u000f\u001a\u00020\u0006HÆ\u0003J'\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006HÆ\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010\u0014\u001a\u00020\u0015HÖ\u0001J\t\u0010\u0016\u001a\u00020\u0003HÖ\u0001R\u0011\u0010\u0002\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0011\u0010\u0005\u001a\u00020\u0006¢\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f¨\u0006\u0017"}, d2 = {"Lcom/proxy/videowidget/repository/WidgetRepository$TrackEntry;", "", "action", "", "filePath", "timestamp", "", "(Ljava/lang/String;Ljava/lang/String;J)V", "getAction", "()Ljava/lang/String;", "getFilePath", "getTimestamp", "()J", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    static final /* data */ class TrackEntry {
        private final String action;
        private final String filePath;
        private final long timestamp;

        public static /* synthetic */ TrackEntry copy$default(TrackEntry trackEntry, String str, String str2, long j, int i, Object obj) {
            if ((i & 1) != 0) {
                str = trackEntry.action;
            }
            if ((i & 2) != 0) {
                str2 = trackEntry.filePath;
            }
            if ((i & 4) != 0) {
                j = trackEntry.timestamp;
            }
            return trackEntry.copy(str, str2, j);
        }

        /* renamed from: component1, reason: from getter */
        public final String getAction() {
            return this.action;
        }

        /* renamed from: component2, reason: from getter */
        public final String getFilePath() {
            return this.filePath;
        }

        /* renamed from: component3, reason: from getter */
        public final long getTimestamp() {
            return this.timestamp;
        }

        public final TrackEntry copy(String action, String filePath, long timestamp) {
            Intrinsics.checkNotNullParameter(action, "action");
            Intrinsics.checkNotNullParameter(filePath, "filePath");
            return new TrackEntry(action, filePath, timestamp);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof TrackEntry)) {
                return false;
            }
            TrackEntry trackEntry = (TrackEntry) other;
            return Intrinsics.areEqual(this.action, trackEntry.action) && Intrinsics.areEqual(this.filePath, trackEntry.filePath) && this.timestamp == trackEntry.timestamp;
        }

        public int hashCode() {
            return (((this.action.hashCode() * 31) + this.filePath.hashCode()) * 31) + Long.hashCode(this.timestamp);
        }

        public String toString() {
            return "TrackEntry(action=" + this.action + ", filePath=" + this.filePath + ", timestamp=" + this.timestamp + ')';
        }

        public TrackEntry(String action, String filePath, long j) {
            Intrinsics.checkNotNullParameter(action, "action");
            Intrinsics.checkNotNullParameter(filePath, "filePath");
            this.action = action;
            this.filePath = filePath;
            this.timestamp = j;
        }

        public final String getAction() {
            return this.action;
        }

        public final String getFilePath() {
            return this.filePath;
        }

        public final long getTimestamp() {
            return this.timestamp;
        }
    }

    /* JADX WARN: Failed to restore switch over string. Please report as a decompilation issue
    java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "jadx.core.dex.visitors.regions.SwitchOverStringVisitor$SwitchData.getNewCases()" is null
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.restoreSwitchOverString(SwitchOverStringVisitor.java:109)
    	at jadx.core.dex.visitors.regions.SwitchOverStringVisitor.visitRegion(SwitchOverStringVisitor.java:66)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:77)
    	at jadx.core.dex.visitors.regions.DepthRegionTraversal.traverseIterativeStepInternal(DepthRegionTraversal.java:82)
     */
    /* JADX WARN: Removed duplicated region for block: B:59:0x01bc A[Catch: all -> 0x028b, TryCatch #2 {all -> 0x028b, blocks: (B:5:0x000e, B:7:0x0021, B:13:0x007e, B:21:0x008d, B:23:0x0097, B:24:0x00a4, B:26:0x00aa, B:31:0x00c2, B:36:0x00d0, B:40:0x00dd, B:43:0x00e7, B:44:0x010b, B:47:0x0115, B:48:0x0136, B:51:0x013f, B:52:0x0162, B:55:0x0169, B:56:0x0189, B:35:0x00cd, B:57:0x01b2, B:62:0x01c3, B:64:0x01ce, B:66:0x01da, B:67:0x01e6, B:69:0x01ed, B:71:0x0205, B:73:0x020b, B:75:0x0211, B:78:0x021d, B:80:0x022a, B:81:0x022d, B:82:0x0230, B:83:0x0236, B:86:0x023e, B:88:0x024a, B:91:0x0258, B:89:0x0252, B:93:0x0276, B:94:0x027e, B:74:0x020e, B:68:0x01e9, B:59:0x01bc, B:18:0x0085, B:19:0x0088, B:20:0x0089, B:8:0x002d, B:9:0x0062, B:11:0x0068, B:12:0x0076, B:16:0x0083), top: B:106:0x000e, inners: #0, #1 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void cleanUserData() {
        /*
            Method dump skipped, instructions count: 682
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.repository.WidgetRepository.cleanUserData():void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final boolean cleanUserData$lambda$22$lambda$19(File file) {
        String name = file.getName();
        Intrinsics.checkNotNullExpressionValue(name, "getName(...)");
        return StringsKt.startsWith$default(name, "media_", false, 2, (Object) null);
    }

    private final void cleanCache() {
        Context context = appContext;
        if (context == null) {
            throw new IllegalStateException("WidgetRepository is not initialized.");
        }
        File cacheDir = context.getCacheDir();
        if (cacheDir != null) {
            FilesKt.deleteRecursively(cacheDir);
        }
    }
}
