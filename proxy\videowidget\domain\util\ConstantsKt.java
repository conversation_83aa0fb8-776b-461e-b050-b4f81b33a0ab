package com.proxy.videowidget.domain.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import kotlin.Metadata;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: Constants.kt */
@Metadata(d1 = {"\u0000(\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0018\u0002\n\u0000\u001a\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003\u001a\u0006\u0010\u0004\u001a\u00020\u0001\u001a\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t\u001a\u0012\u0010\n\u001a\u00020\u000b*\u00020\f2\u0006\u0010\u0007\u001a\u00020\u0003¨\u0006\r"}, d2 = {"checkStoragePermission", "", "mContext", "Landroid/content/Context;", "isAndroid13", "saveBitmapToFile", "Ljava/io/File;", "context", "bitmap", "Landroid/graphics/Bitmap;", "getMediaDuration", "", "Landroid/net/Uri;", "app_release"}, k = 2, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class ConstantsKt {
    public static final boolean isAndroid13() {
        return Build.VERSION.SDK_INT >= 33;
    }

    public static final boolean checkStoragePermission(Context mContext) {
        Intrinsics.checkNotNullParameter(mContext, "mContext");
        return ContextCompat.checkSelfPermission(mContext, isAndroid13() ? "android.permission.READ_MEDIA_VIDEO" : "android.permission.READ_EXTERNAL_STORAGE") == 0;
    }

    public static final File saveBitmapToFile(Context context, Bitmap bitmap) throws IOException {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(bitmap, "bitmap");
        File file = new File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "VideoFrames");
        if (!file.exists()) {
            file.mkdirs();
        }
        File file2 = new File(file, "image_" + new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date()) + ".jpg");
        FileOutputStream fileOutputStream = new FileOutputStream(file2);
        try {
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
            CloseableKt.closeFinally(fileOutputStream, null);
            return file2;
        } finally {
        }
    }

    public static final long getMediaDuration(Uri uri, Context context) throws SecurityException, IOException, IllegalArgumentException {
        Long longOrNull;
        Intrinsics.checkNotNullParameter(uri, "<this>");
        Intrinsics.checkNotNullParameter(context, "context");
        try {
            MediaMetadataRetriever mediaMetadataRetriever = new MediaMetadataRetriever();
            mediaMetadataRetriever.setDataSource(context, uri);
            String strExtractMetadata = mediaMetadataRetriever.extractMetadata(9);
            mediaMetadataRetriever.release();
            if (strExtractMetadata == null || (longOrNull = StringsKt.toLongOrNull(strExtractMetadata)) == null) {
                return 0L;
            }
            return longOrNull.longValue();
        } catch (Exception unused) {
            return 0L;
        }
    }
}
