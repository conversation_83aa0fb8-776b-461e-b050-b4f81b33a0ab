package com.proxy.videowidget.presentation;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.proxy.videowidget.domain.service.WidgetService;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: BootReceiver.kt */
@Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u001c\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002¨\u0006\n"}, d2 = {"Lcom/proxy/videowidget/presentation/BootReceiver;", "Landroid/content/BroadcastReceiver;", "()V", "onReceive", "", "context", "Landroid/content/Context;", "intent", "Landroid/content/Intent;", "startServiceCompat", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class BootReceiver extends BroadcastReceiver {
    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        if (Intrinsics.areEqual(intent != null ? intent.getAction() : null, "android.intent.action.BOOT_COMPLETED")) {
            if (context != null) {
                Log.d("BootReceiver", "Boot completed, starting WidgetService");
                startServiceCompat(context);
            } else {
                Log.e("BootReceiver", "Context is null, cannot start WidgetService");
            }
        }
    }

    private final void startServiceCompat(Context context) {
        Intent intent = new Intent(context, (Class<?>) WidgetService.class);
        try {
            if (Build.VERSION.SDK_INT >= 26) {
                context.startForegroundService(intent);
                Log.d("BootReceiver", "Started WidgetService as a foreground service");
            } else {
                context.startService(intent);
                Log.d("BootReceiver", "Started WidgetService as a background service");
            }
        } catch (Exception e) {
            Log.e("BootReceiver", "Failed to start WidgetService: " + e.getMessage(), e);
        }
    }
}
