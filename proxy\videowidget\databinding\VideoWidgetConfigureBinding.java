package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class VideoWidgetConfigureBinding implements ViewBinding {
    public final AppCompatButton addButton;
    public final TextView headerText;
    private final ConstraintLayout rootView;
    public final ImageView thumbnail;
    public final CardView thumbnailCard;
    public final AppCompatButton uploadVideo;

    private VideoWidgetConfigureBinding(ConstraintLayout constraintLayout, AppCompatButton appCompatButton, TextView textView, ImageView imageView, CardView cardView, AppCompatButton appCompatButton2) {
        this.rootView = constraintLayout;
        this.addButton = appCompatButton;
        this.headerText = textView;
        this.thumbnail = imageView;
        this.thumbnailCard = cardView;
        this.uploadVideo = appCompatButton2;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static VideoWidgetConfigureBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static VideoWidgetConfigureBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.video_widget_configure, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static VideoWidgetConfigureBinding bind(View view) {
        int i = R.id.add_button;
        AppCompatButton appCompatButton = (AppCompatButton) ViewBindings.findChildViewById(view, i);
        if (appCompatButton != null) {
            i = R.id.headerText;
            TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
            if (textView != null) {
                i = R.id.thumbnail;
                ImageView imageView = (ImageView) ViewBindings.findChildViewById(view, i);
                if (imageView != null) {
                    i = R.id.thumbnailCard;
                    CardView cardView = (CardView) ViewBindings.findChildViewById(view, i);
                    if (cardView != null) {
                        i = R.id.uploadVideo;
                        AppCompatButton appCompatButton2 = (AppCompatButton) ViewBindings.findChildViewById(view, i);
                        if (appCompatButton2 != null) {
                            return new VideoWidgetConfigureBinding((ConstraintLayout) view, appCompatButton, textView, imageView, cardView, appCompatButton2);
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
