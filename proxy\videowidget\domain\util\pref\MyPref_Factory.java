package com.proxy.videowidget.domain.util.pref;

import android.content.Context;
import dagger.internal.Factory;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class MyPref_Factory implements Factory<MyPref> {
    private final Provider<Context> contextProvider;

    public MyPref_Factory(Provider<Context> provider) {
        this.contextProvider = provider;
    }

    @Override // javax.inject.Provider
    public MyPref get() {
        return newInstance(this.contextProvider.get());
    }

    public static MyPref_Factory create(Provider<Context> provider) {
        return new MyPref_Factory(provider);
    }

    public static MyPref newInstance(Context context) {
        return new MyPref(context);
    }
}
