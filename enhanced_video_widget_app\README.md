# Enhanced Video Widget App

An Android widget application that allows users to display videos on their home screen with **pause and play functionality**. This is an enhanced version of the original video widget app with additional playback controls.

## Features

### 🎥 Core Functionality
- **Video Selection**: Choose videos from your device's gallery
- **Home Screen Widgets**: Display videos directly on your home screen
- **Pause/Play Controls**: Tap the pause/play button to control video playback
- **Frame-by-Frame Animation**: Smooth video playback using extracted frames
- **Widget Management**: Create, configure, and delete multiple video widgets

### 🎮 Enhanced Controls
- **Play/Pause Button**: Dedicated button overlay for easy playback control
- **Visual Status Indicators**: Clear indication of playing/paused state
- **Settings Button**: Quick access to widget configuration
- **Frame Counter**: Debug information showing current frame position

### 🏗️ Technical Features
- **Background Processing**: Efficient video frame extraction using WorkManager
- **State Persistence**: Widget states are saved and restored across device reboots
- **Memory Optimization**: Smart frame caching and bitmap management
- **Responsive UI**: Adaptive layouts that work on different screen sizes

## Installation

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd enhanced_video_widget_app
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the `enhanced_video_widget_app` folder

3. **Build and Install**
   - Connect your Android device or start an emulator
   - Click "Run" or press `Ctrl+R` (Windows/Linux) / `Cmd+R` (Mac)

## Usage

### Creating a Video Widget

1. **Launch the App**
   - Open the "Enhanced Video Widget" app from your app drawer

2. **Add a New Widget**
   - Tap the "+" (plus) button in the bottom-right corner
   - Grant storage permission when prompted
   - Select a video from your device's gallery

3. **Configure the Widget**
   - The app will automatically process the video and extract frames
   - The widget will be created and ready to use

### Adding Widget to Home Screen

1. **Long Press on Home Screen**
   - Long press on an empty area of your home screen
   - Select "Widgets" from the menu

2. **Find Enhanced Video Widget**
   - Scroll through the widget list
   - Find "Enhanced Video Widget"
   - Drag it to your desired location

3. **Configure Widget (if prompted)**
   - Select a video for the widget
   - Customize the widget name if desired
   - Tap "Create Widget"

### Using Widget Controls

- **Play/Pause**: Tap the play/pause button in the bottom-left corner of the widget
- **Settings**: Tap the settings button in the bottom-right corner (future feature)
- **General Interaction**: Tap anywhere on the widget to toggle play/pause

## Project Structure

```
enhanced_video_widget_app/
├── app/
│   ├── src/main/
│   │   ├── java/com/enhanced/videowidget/
│   │   │   ├── data/
│   │   │   │   ├── model/Widget.kt
│   │   │   │   └── repository/WidgetRepository.kt
│   │   │   ├── service/WidgetUpdateService.kt
│   │   │   ├── ui/
│   │   │   │   ├── MainActivity.kt
│   │   │   │   ├── WidgetConfigurationActivity.kt
│   │   │   │   └── adapter/WidgetListAdapter.kt
│   │   │   ├── utils/VideoFrameExtractor.kt
│   │   │   ├── widget/EnhancedVideoWidget.kt
│   │   │   └── worker/WidgetUpdateWorker.kt
│   │   ├── res/
│   │   │   ├── drawable/ (UI icons and backgrounds)
│   │   │   ├── layout/ (XML layouts)
│   │   │   ├── values/ (strings, colors, themes)
│   │   │   └── xml/ (widget metadata)
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── build.gradle
├── settings.gradle
└── README.md
```

## Key Components

### 1. Enhanced Widget Data Model
- **Widget.kt**: Data class with pause/play state management
- **State tracking**: Current frame index, playing status, pause status
- **Helper methods**: Frame navigation, state toggling

### 2. Widget Repository
- **WidgetRepository.kt**: Centralized data management
- **State persistence**: Save/load widget configurations
- **Frame caching**: Efficient bitmap storage and retrieval

### 3. Video Frame Processing
- **VideoFrameExtractor.kt**: Extract frames from video files
- **MediaMetadataRetriever**: Native Android video processing
- **Frame optimization**: Resize and compress frames for widgets

### 4. Widget Provider
- **EnhancedVideoWidget.kt**: Main widget provider class
- **Click handling**: Play/pause, settings, general interactions
- **UI updates**: Dynamic button states and status indicators

### 5. Background Processing
- **WidgetUpdateWorker.kt**: Frame animation using WorkManager
- **Efficient scheduling**: Only runs when widgets are playing
- **State-aware**: Respects pause/play states

## Permissions

The app requires the following permissions:

- **READ_EXTERNAL_STORAGE**: Access videos from device storage
- **WRITE_EXTERNAL_STORAGE**: Cache processed frames (Android 9 and below)
- **WAKE_LOCK**: Keep processing active during frame extraction
- **FOREGROUND_SERVICE**: Background video processing

## Compatibility

- **Minimum SDK**: Android 7.0 (API level 24)
- **Target SDK**: Android 14 (API level 34)
- **Architecture**: Supports all Android architectures (ARM, x86)

## Differences from Original

### Enhanced Features
1. **Pause/Play Controls**: Added dedicated pause/play button with visual feedback
2. **State Management**: Persistent playback state across app restarts
3. **Improved UI**: Modern Material Design with better user experience
4. **Better Performance**: Optimized frame processing and memory management
5. **Widget Configuration**: Dedicated configuration screen for better setup

### Technical Improvements
1. **Kotlin**: Modern Kotlin codebase instead of Java
2. **WorkManager**: Reliable background processing
3. **LiveData**: Reactive data updates
4. **View Binding**: Type-safe view references
5. **Material Components**: Modern UI components

## Troubleshooting

### Widget Not Updating
- Check if the app has battery optimization disabled
- Ensure storage permissions are granted
- Try recreating the widget

### Video Not Loading
- Verify the video file still exists on the device
- Check if the video format is supported (MP4, AVI, MOV)
- Ensure sufficient storage space for frame caching

### Performance Issues
- Limit the number of active widgets (recommended: 3-5)
- Use shorter videos for better performance
- Clear app cache if storage is full

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on different devices
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
