package com.proxy.videowidget.data.di;

import android.appwidget.AppWidgetManager;
import android.content.Context;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class AppModule_ProvideAppWidgetManagerFactory implements Factory<AppWidgetManager> {
    private final Provider<Context> contextProvider;

    public AppModule_ProvideAppWidgetManagerFactory(Provider<Context> provider) {
        this.contextProvider = provider;
    }

    @Override // javax.inject.Provider
    public AppWidgetManager get() {
        return provideAppWidgetManager(this.contextProvider.get());
    }

    public static AppModule_ProvideAppWidgetManagerFactory create(Provider<Context> provider) {
        return new AppModule_ProvideAppWidgetManagerFactory(provider);
    }

    public static AppWidgetManager provideAppWidgetManager(Context context) {
        return (AppWidgetManager) Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideAppWidgetManager(context));
    }
}
