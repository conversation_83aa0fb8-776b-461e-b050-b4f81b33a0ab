package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.VideoView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentInstructionOldBinding implements ViewBinding {
    public final TextView headerTextView;
    public final Button rateBtn;
    private final ConstraintLayout rootView;
    public final Button shareBtn;
    public final TextView textView;
    public final VideoView videoView;

    private FragmentInstructionOldBinding(ConstraintLayout constraintLayout, TextView textView, Button button, Button button2, TextView textView2, VideoView videoView) {
        this.rootView = constraintLayout;
        this.headerTextView = textView;
        this.rateBtn = button;
        this.shareBtn = button2;
        this.textView = textView2;
        this.videoView = videoView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentInstructionOldBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentInstructionOldBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_instruction_old, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentInstructionOldBinding bind(View view) {
        int i = R.id.headerTextView;
        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
        if (textView != null) {
            i = R.id.rateBtn;
            Button button = (Button) ViewBindings.findChildViewById(view, i);
            if (button != null) {
                i = R.id.shareBtn;
                Button button2 = (Button) ViewBindings.findChildViewById(view, i);
                if (button2 != null) {
                    i = R.id.textView;
                    TextView textView2 = (TextView) ViewBindings.findChildViewById(view, i);
                    if (textView2 != null) {
                        i = R.id.videoView;
                        VideoView videoView = (VideoView) ViewBindings.findChildViewById(view, i);
                        if (videoView != null) {
                            return new FragmentInstructionOldBinding((ConstraintLayout) view, textView, button, button2, textView2, videoView);
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
