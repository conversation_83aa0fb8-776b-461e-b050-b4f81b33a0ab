package com.proxy.videowidget.presentation.widget;

import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.content.Intent;
import dagger.hilt.android.internal.managers.BroadcastReceiverComponentManager;
import dagger.hilt.internal.UnsafeCasts;

/* loaded from: classes3.dex */
public abstract class Hilt_VideoWidget extends AppWidgetProvider {
    private volatile boolean injected = false;
    private final Object injectedLock = new Object();

    @Override // android.appwidget.AppWidgetProvider, android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        inject(context);
        super.onReceive(context, intent);
    }

    protected void inject(Context context) {
        if (this.injected) {
            return;
        }
        synchronized (this.injectedLock) {
            if (!this.injected) {
                ((VideoWidget_GeneratedInjector) BroadcastReceiverComponentManager.generatedComponent(context)).injectVideoWidget((VideoWidget) UnsafeCasts.unsafeCast(this));
                this.injected = true;
            }
        }
    }
}
