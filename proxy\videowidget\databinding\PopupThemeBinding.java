package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class PopupThemeBinding implements ViewBinding {
    public final RadioButton darkMode;
    public final RadioButton lightMode;
    private final LinearLayout rootView;
    public final RadioButton systemDefault;
    public final RadioGroup themeRadioGroup;
    public final TextView titleText;

    private PopupThemeBinding(LinearLayout linearLayout, RadioButton radioButton, RadioButton radioButton2, RadioButton radioButton3, RadioGroup radioGroup, TextView textView) {
        this.rootView = linearLayout;
        this.darkMode = radioButton;
        this.lightMode = radioButton2;
        this.systemDefault = radioButton3;
        this.themeRadioGroup = radioGroup;
        this.titleText = textView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public LinearLayout getRoot() {
        return this.rootView;
    }

    public static PopupThemeBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static PopupThemeBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.popup_theme, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static PopupThemeBinding bind(View view) {
        int i = R.id.darkMode;
        RadioButton radioButton = (RadioButton) ViewBindings.findChildViewById(view, i);
        if (radioButton != null) {
            i = R.id.lightMode;
            RadioButton radioButton2 = (RadioButton) ViewBindings.findChildViewById(view, i);
            if (radioButton2 != null) {
                i = R.id.systemDefault;
                RadioButton radioButton3 = (RadioButton) ViewBindings.findChildViewById(view, i);
                if (radioButton3 != null) {
                    i = R.id.themeRadioGroup;
                    RadioGroup radioGroup = (RadioGroup) ViewBindings.findChildViewById(view, i);
                    if (radioGroup != null) {
                        i = R.id.titleText;
                        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
                        if (textView != null) {
                            return new PopupThemeBinding((LinearLayout) view, radioButton, radioButton2, radioButton3, radioGroup, textView);
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
