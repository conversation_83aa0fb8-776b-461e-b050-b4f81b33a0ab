package com.proxy.videowidget.domain.util.pref;

import dagger.internal.Factory;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class PrefController_Factory implements Factory<PrefController> {
    private final Provider<MyPref> myPrefProvider;

    public PrefController_Factory(Provider<MyPref> provider) {
        this.myPrefProvider = provider;
    }

    @Override // javax.inject.Provider
    public PrefController get() {
        return newInstance(this.myPrefProvider.get());
    }

    public static PrefController_Factory create(Provider<MyPref> provider) {
        return new PrefController_Factory(provider);
    }

    public static PrefController newInstance(MyPref myPref) {
        return new PrefController(myPref);
    }
}
