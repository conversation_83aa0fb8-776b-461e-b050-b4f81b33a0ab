package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentAccountBinding implements ViewBinding {
    public final TextView emptyListText;
    public final TextView headerText;
    public final MaterialButton moreBtn;
    public final RecyclerView recyclerViewWidgets;
    private final ConstraintLayout rootView;

    private FragmentAccountBinding(ConstraintLayout constraintLayout, TextView textView, TextView textView2, MaterialButton materialButton, RecyclerView recyclerView) {
        this.rootView = constraintLayout;
        this.emptyListText = textView;
        this.headerText = textView2;
        this.moreBtn = materialButton;
        this.recyclerViewWidgets = recyclerView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentAccountBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentAccountBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_account, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentAccountBinding bind(View view) {
        int i = R.id.emptyListText;
        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
        if (textView != null) {
            i = R.id.headerText;
            TextView textView2 = (TextView) ViewBindings.findChildViewById(view, i);
            if (textView2 != null) {
                i = R.id.moreBtn;
                MaterialButton materialButton = (MaterialButton) ViewBindings.findChildViewById(view, i);
                if (materialButton != null) {
                    i = R.id.recyclerViewWidgets;
                    RecyclerView recyclerView = (RecyclerView) ViewBindings.findChildViewById(view, i);
                    if (recyclerView != null) {
                        return new FragmentAccountBinding((ConstraintLayout) view, textView, textView2, materialButton, recyclerView);
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
