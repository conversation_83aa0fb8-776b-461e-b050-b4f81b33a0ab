package com.proxy.videowidget.data.di;

import android.content.Context;
import androidx.core.app.NotificationCompat;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class AppModule_ProvideNotificationBuilderFactory implements Factory<NotificationCompat.Builder> {
    private final Provider<Context> contextProvider;

    public AppModule_ProvideNotificationBuilderFactory(Provider<Context> provider) {
        this.contextProvider = provider;
    }

    @Override // javax.inject.Provider
    public NotificationCompat.Builder get() {
        return provideNotificationBuilder(this.contextProvider.get());
    }

    public static AppModule_ProvideNotificationBuilderFactory create(Provider<Context> provider) {
        return new AppModule_ProvideNotificationBuilderFactory(provider);
    }

    public static NotificationCompat.Builder provideNotificationBuilder(Context context) {
        return (NotificationCompat.Builder) Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideNotificationBuilder(context));
    }
}
