package com.proxy.videowidget.domain.util.decoder;

import androidx.constraintlayout.widget.ConstraintLayout;
import kotlin.Metadata;

/* compiled from: IVideoFrameExtractor.kt */
@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J \u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH&J\u0010\u0010\n\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\fH&¨\u0006\r"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/IVideoFrameExtractor;", "", "onAllFrameExtracted", "", "processedFrameCount", "", "processedTimeMs", "", "frameRate", "", "onCurrentFrameExtracted", "currentFrame", "Lcom/proxy/videowidget/domain/util/decoder/Frame;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public interface IVideoFrameExtractor {
    void onAllFrameExtracted(int processedFrameCount, long processedTimeMs, float frameRate);

    void onCurrentFrameExtracted(Frame currentFrame);
}
