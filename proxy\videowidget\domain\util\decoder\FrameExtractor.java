package com.proxy.videowidget.domain.util.decoder;

import android.graphics.Bitmap;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.os.Build;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import kotlin.Metadata;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: FrameExtractor.kt */
@Metadata(d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 .2\u00020\u0001:\u0001.B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J8\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020%H\u0002J\u0016\u0010&\u001a\u00020\u001a2\u0006\u0010'\u001a\u00020%2\u0006\u0010$\u001a\u00020%J \u0010(\u001a\u00020\u001a2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020\u00062\u0006\u0010$\u001a\u00020%H\u0002J\u0010\u0010,\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\u0006\u0010-\u001a\u00020\u001aR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082D¢\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u00020\nX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\t\u0010\u000b\"\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\nX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u001a\u0010\u0010\u001a\u00020\u0006X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u001a\u0010\u0015\u001a\u00020\u0006X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0012\"\u0004\b\u0017\u0010\u0014R\u000e\u0010\u0018\u001a\u00020\u0006X\u0082D¢\u0006\u0002\n\u0000¨\u0006/"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/FrameExtractor;", "", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "Lcom/proxy/videowidget/domain/util/decoder/IVideoFrameExtractor;", "(Lcom/proxy/videowidget/domain/util/decoder/IVideoFrameExtractor;)V", "MAX_FRAMES", "", "SDK_VERSION_INT", "frameCount", "isPortrait", "", "()Z", "setPortrait", "(Z)V", "isTerminated", "jpegQuality", "savedFrameHeight", "getSavedFrameHeight", "()I", "setSavedFrameHeight", "(I)V", "savedFrameWidth", "getSavedFrameWidth", "setSavedFrameWidth", "size", "doExtract", "", "extractor", "Landroid/media/MediaExtractor;", "trackIndex", "decoder", "Landroid/media/MediaCodec;", "outputSurface", "Lcom/proxy/videowidget/domain/util/decoder/CodecOutputSurface;", "frameRate", "", "outputDirPath", "", "extractFrames", "inputFilePath", "saveFrameAsImage", TypedValues.AttributesType.S_FRAME, "Lcom/proxy/videowidget/domain/util/decoder/Frame;", "frameIndex", "selectTrack", "terminate", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class FrameExtractor {
    private static final String TAG = "FrameExtractor";
    private int MAX_FRAMES;
    private final int SDK_VERSION_INT;
    private final int frameCount;
    private boolean isPortrait;
    private volatile boolean isTerminated;
    private int jpegQuality;
    private final IVideoFrameExtractor listener;
    private int savedFrameHeight;
    private int savedFrameWidth;
    private final int size;

    public FrameExtractor(IVideoFrameExtractor listener) {
        Intrinsics.checkNotNullParameter(listener, "listener");
        this.listener = listener;
        this.size = 1;
        this.frameCount = Integer.MAX_VALUE;
        this.SDK_VERSION_INT = Build.VERSION.SDK_INT;
        this.jpegQuality = 30;
    }

    /* renamed from: isPortrait, reason: from getter */
    public final boolean getIsPortrait() {
        return this.isPortrait;
    }

    public final void setPortrait(boolean z) {
        this.isPortrait = z;
    }

    public final int getSavedFrameWidth() {
        return this.savedFrameWidth;
    }

    public final void setSavedFrameWidth(int i) {
        this.savedFrameWidth = i;
    }

    public final int getSavedFrameHeight() {
        return this.savedFrameHeight;
    }

    public final void setSavedFrameHeight(int i) {
        this.savedFrameHeight = i;
    }

    public final void terminate() {
        this.isTerminated = true;
    }

    /* JADX WARN: Removed duplicated region for block: B:105:0x01b8  */
    /* JADX WARN: Removed duplicated region for block: B:107:0x01bd  */
    /* JADX WARN: Removed duplicated region for block: B:109:0x01c2  */
    /* JADX WARN: Removed duplicated region for block: B:111:0x01c7  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x00bf  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x00c1 A[Catch: all -> 0x0057, IOException -> 0x005c, TRY_ENTER, TRY_LEAVE, TryCatch #13 {IOException -> 0x005c, all -> 0x0057, blocks: (B:13:0x0050, B:24:0x007d, B:43:0x00d0, B:48:0x00ff, B:53:0x010c, B:38:0x00c1, B:28:0x009c), top: B:119:0x0050 }] */
    /* JADX WARN: Removed duplicated region for block: B:43:0x00d0 A[Catch: all -> 0x0057, IOException -> 0x005c, TRY_ENTER, TRY_LEAVE, TryCatch #13 {IOException -> 0x005c, all -> 0x0057, blocks: (B:13:0x0050, B:24:0x007d, B:43:0x00d0, B:48:0x00ff, B:53:0x010c, B:38:0x00c1, B:28:0x009c), top: B:119:0x0050 }] */
    /* JADX WARN: Removed duplicated region for block: B:45:0x00d5 A[Catch: all -> 0x017e, IOException -> 0x0182, TRY_ENTER, TryCatch #11 {IOException -> 0x0182, all -> 0x017e, blocks: (B:9:0x0031, B:11:0x003e, B:21:0x0069, B:31:0x00ab, B:40:0x00c7, B:46:0x00db, B:51:0x0108, B:56:0x0114, B:55:0x0110, B:50:0x0104, B:45:0x00d5, B:26:0x0084, B:30:0x00a5, B:20:0x0061), top: B:123:0x0031 }] */
    /* JADX WARN: Removed duplicated region for block: B:48:0x00ff A[Catch: all -> 0x0057, IOException -> 0x005c, TRY_ENTER, TRY_LEAVE, TryCatch #13 {IOException -> 0x005c, all -> 0x0057, blocks: (B:13:0x0050, B:24:0x007d, B:43:0x00d0, B:48:0x00ff, B:53:0x010c, B:38:0x00c1, B:28:0x009c), top: B:119:0x0050 }] */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0104 A[Catch: all -> 0x017e, IOException -> 0x0182, TRY_ENTER, TryCatch #11 {IOException -> 0x0182, all -> 0x017e, blocks: (B:9:0x0031, B:11:0x003e, B:21:0x0069, B:31:0x00ab, B:40:0x00c7, B:46:0x00db, B:51:0x0108, B:56:0x0114, B:55:0x0110, B:50:0x0104, B:45:0x00d5, B:26:0x0084, B:30:0x00a5, B:20:0x0061), top: B:123:0x0031 }] */
    /* JADX WARN: Removed duplicated region for block: B:53:0x010c A[Catch: all -> 0x0057, IOException -> 0x005c, TRY_ENTER, TRY_LEAVE, TryCatch #13 {IOException -> 0x005c, all -> 0x0057, blocks: (B:13:0x0050, B:24:0x007d, B:43:0x00d0, B:48:0x00ff, B:53:0x010c, B:38:0x00c1, B:28:0x009c), top: B:119:0x0050 }] */
    /* JADX WARN: Removed duplicated region for block: B:55:0x0110 A[Catch: all -> 0x017e, IOException -> 0x0182, TRY_ENTER, TryCatch #11 {IOException -> 0x0182, all -> 0x017e, blocks: (B:9:0x0031, B:11:0x003e, B:21:0x0069, B:31:0x00ab, B:40:0x00c7, B:46:0x00db, B:51:0x0108, B:56:0x0114, B:55:0x0110, B:50:0x0104, B:45:0x00d5, B:26:0x0084, B:30:0x00a5, B:20:0x0061), top: B:123:0x0031 }] */
    /* JADX WARN: Removed duplicated region for block: B:61:0x0148  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x014d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void extractFrames(java.lang.String r22, java.lang.String r23) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 459
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.util.decoder.FrameExtractor.extractFrames(java.lang.String, java.lang.String):void");
    }

    private final int selectTrack(MediaExtractor extractor) {
        int trackCount = extractor.getTrackCount();
        for (int i = 0; i < trackCount; i++) {
            MediaFormat trackFormat = extractor.getTrackFormat(i);
            Intrinsics.checkNotNullExpressionValue(trackFormat, "getTrackFormat(...)");
            String string = trackFormat.getString("mime");
            Intrinsics.checkNotNull(string);
            if (StringsKt.startsWith$default(string, "video/", false, 2, (Object) null)) {
                return i;
            }
        }
        return -1;
    }

    /* JADX WARN: Removed duplicated region for block: B:37:0x00c4 A[PHI: r14
  0x00c4: PHI (r14v2 boolean) = (r14v1 boolean), (r14v1 boolean), (r14v1 boolean), (r14v4 boolean) binds: [B:18:0x0083, B:20:0x0087, B:22:0x008e, B:31:0x00a1] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final void doExtract(android.media.MediaExtractor r20, int r21, android.media.MediaCodec r22, com.proxy.videowidget.domain.util.decoder.CodecOutputSurface r23, float r24, java.lang.String r25) throws android.media.MediaCodec.CryptoException, java.io.IOException {
        /*
            r19 = this;
            r0 = r19
            r1 = r21
            r9 = r22
            r10 = r23
            java.nio.ByteBuffer[] r11 = r22.getInputBuffers()
            java.lang.String r2 = "getInputBuffers(...)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r11, r2)
            android.media.MediaCodec$BufferInfo r12 = new android.media.MediaCodec$BufferInfo
            r12.<init>()
            r13 = 0
            r14 = r13
            r15 = r14
            r16 = r15
        L1b:
            if (r14 != 0) goto Lc9
            boolean r2 = r0.isTerminated
            if (r2 != 0) goto Lc9
            r8 = 1
            r6 = 10000(0x2710, float:1.4013E-41)
            if (r16 != 0) goto L82
            long r2 = (long) r6
            int r3 = r9.dequeueInputBuffer(r2)
            if (r3 < 0) goto L82
            r2 = r11[r3]
            r7 = r20
            int r5 = r7.readSampleData(r2, r13)
            if (r5 >= 0) goto L4a
            r16 = 0
            r18 = 4
            r4 = 0
            r5 = 0
            r2 = r22
            r13 = r6
            r6 = r16
            r8 = r18
            r2.queueInputBuffer(r3, r4, r5, r6, r8)
            r16 = 1
            goto L83
        L4a:
            r13 = r6
            int r2 = r20.getSampleTrackIndex()
            if (r2 == r1) goto L73
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r4 = "WEIRD: got sample from track "
            r2.<init>(r4)
            int r4 = r20.getSampleTrackIndex()
            java.lang.StringBuilder r2 = r2.append(r4)
            java.lang.String r4 = ", expected "
            java.lang.StringBuilder r2 = r2.append(r4)
            java.lang.StringBuilder r2 = r2.append(r1)
            java.lang.String r2 = r2.toString()
            java.lang.String r4 = "FrameExtractor"
            android.util.Log.w(r4, r2)
        L73:
            long r6 = r20.getSampleTime()
            r8 = 0
            r4 = 0
            r2 = r22
            r2.queueInputBuffer(r3, r4, r5, r6, r8)
            r20.advance()
            goto L83
        L82:
            r13 = r6
        L83:
            if (r14 != 0) goto Lc4
            boolean r2 = r0.isTerminated
            if (r2 != 0) goto Lc4
            long r2 = (long) r13
            int r2 = r9.dequeueOutputBuffer(r12, r2)
            if (r2 < 0) goto Lc4
            int r3 = r12.flags
            r3 = r3 & 4
            if (r3 == 0) goto L97
            r14 = 1
        L97:
            int r3 = r12.size
            if (r3 == 0) goto L9d
            r8 = 1
            goto L9e
        L9d:
            r8 = 0
        L9e:
            r9.releaseOutputBuffer(r2, r8)
            if (r8 == 0) goto Lc4
            r23.awaitNewImage()
            r2 = 1
            r10.drawImage(r2)
            int r2 = r0.MAX_FRAMES
            if (r15 >= r2) goto Lbf
            long r2 = r12.presentationTimeUs
            com.proxy.videowidget.domain.util.decoder.Frame r2 = r10.retrieveFrame(r15, r2)
            r3 = r25
            r0.saveFrameAsImage(r2, r15, r3)
            com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor r4 = r0.listener
            r4.onCurrentFrameExtracted(r2)
            goto Lc1
        Lbf:
            r3 = r25
        Lc1:
            int r15 = r15 + 1
            goto Lc6
        Lc4:
            r3 = r25
        Lc6:
            r13 = 0
            goto L1b
        Lc9:
            com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor r1 = r0.listener
            long r2 = java.lang.System.nanoTime()
            r4 = r24
            r1.onAllFrameExtracted(r15, r2, r4)
            r19.terminate()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.util.decoder.FrameExtractor.doExtract(android.media.MediaExtractor, int, android.media.MediaCodec, com.proxy.videowidget.domain.util.decoder.CodecOutputSurface, float, java.lang.String):void");
    }

    private final void saveFrameAsImage(Frame frame, int frameIndex, String outputDirPath) throws IOException {
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(frame.getWidth(), frame.getHeight(), Bitmap.Config.ARGB_8888);
        Intrinsics.checkNotNullExpressionValue(bitmapCreateBitmap, "createBitmap(...)");
        ByteBuffer byteBuffer = frame.getByteBuffer();
        if (byteBuffer != null) {
            bitmapCreateBitmap.copyPixelsFromBuffer(byteBuffer);
        }
        File file = new File(outputDirPath, "frame_" + frameIndex + ".jpeg");
        File parentFile = file.getParentFile();
        if (parentFile != null) {
            parentFile.mkdirs();
        }
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        try {
            bitmapCreateBitmap.compress(Bitmap.CompressFormat.JPEG, this.jpegQuality, fileOutputStream);
            CloseableKt.closeFinally(fileOutputStream, null);
            bitmapCreateBitmap.recycle();
        } finally {
        }
    }
}
