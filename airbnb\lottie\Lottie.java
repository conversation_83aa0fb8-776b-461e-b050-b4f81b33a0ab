package com.airbnb.lottie;

/* loaded from: classes.dex */
public class Lottie {
    private Lottie() {
    }

    public static void initialize(LottieConfig lottieConfig) {
        <PERSON>.setFetcher(lottieConfig.networkFetcher);
        <PERSON><PERSON>setCacheProvider(lottieConfig.cacheProvider);
        <PERSON>.setTraceEnabled(lottieConfig.enableSystraceMarkers);
        L.setNetworkCacheEnabled(lottieConfig.enableNetworkCache);
        L.setNetworkCacheEnabled(lottieConfig.enableNetworkCache);
        L.setDisablePathInterpolatorCache(lottieConfig.disablePathInterpolatorCache);
    }
}
