package com.proxy.videowidget.domain.worker;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.appwidget.AppWidgetManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import androidx.work.CoroutineWorker;
import androidx.work.ForegroundInfo;
import androidx.work.WorkerParameters;
import com.google.android.exoplayer2.upstream.CmcdHeadersFactory;
import com.proxy.videowidget.R;
import com.proxy.videowidget.domain.util.decoder.Frame;
import com.proxy.videowidget.domain.util.decoder.FrameExtractor;
import com.proxy.videowidget.domain.util.decoder.GifFrameExtractor;
import com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor;
import com.proxy.videowidget.presentation.MainActivity;
import com.proxy.videowidget.presentation.widget.VideoWidget;
import com.proxy.videowidget.presentation.widget.Widget;
import com.proxy.videowidget.repository.WidgetRepository;
import java.io.File;
import java.util.Iterator;
import java.util.List;
import kotlin.Metadata;
import kotlin.ResultKt;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlin.coroutines.intrinsics.IntrinsicsKt;
import kotlin.coroutines.jvm.internal.Boxing;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlin.coroutines.jvm.internal.DebugMetadata;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.Dispatchers;

/* compiled from: WidgetUpdateWorker.kt */
@Metadata(d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\u0018\u0000 #2\u00020\u0001:\u0001#B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005¢\u0006\u0002\u0010\u0006J\b\u0010\u0007\u001a\u00020\bH\u0002J\b\u0010\t\u001a\u00020\nH\u0002J\u000e\u0010\u000b\u001a\u00020\fH\u0096@¢\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\bH\u0096@¢\u0006\u0002\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J0\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0015H\u0082@¢\u0006\u0002\u0010\u0018J.\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u0010H\u0082@¢\u0006\u0002\u0010\u001fJ\u0018\u0010 \u001a\u00020!2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020\u0010H\u0002¨\u0006$"}, d2 = {"Lcom/proxy/videowidget/domain/worker/WidgetUpdateWorker;", "Landroidx/work/CoroutineWorker;", "context", "Landroid/content/Context;", "workerParams", "Landroidx/work/WorkerParameters;", "(Landroid/content/Context;Landroidx/work/WorkerParameters;)V", "createForegroundInfo", "Landroidx/work/ForegroundInfo;", "createNotification", "Landroid/app/Notification;", "doWork", "Landroidx/work/ListenableWorker$Result;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getForegroundInfo", "loadCurrentFrameIndex", "", "appWidgetId", "processFrames", "", "mediaType", "", "inputPath", "framesDirectory", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processFramesLoop", "framesDir", "Ljava/io/File;", "framerate", "", "startIndex", "(ILjava/io/File;FILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveCurrentFrameIndex", "", "index", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WidgetUpdateWorker extends CoroutineWorker {
    public static final String CHANNEL_ID = "WidgetUpdateChannel";
    public static final int NOTIFICATION_ID = 2;

    /* compiled from: WidgetUpdateWorker.kt */
    @Metadata(k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.worker.WidgetUpdateWorker", f = "WidgetUpdateWorker.kt", i = {}, l = {59}, m = "doWork", n = {}, s = {})
    /* renamed from: com.proxy.videowidget.domain.worker.WidgetUpdateWorker$doWork$1, reason: invalid class name */
    static final class AnonymousClass1 extends ContinuationImpl {
        int label;
        /* synthetic */ Object result;

        AnonymousClass1(Continuation<? super AnonymousClass1> continuation) {
            super(continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            this.result = obj;
            this.label |= Integer.MIN_VALUE;
            return WidgetUpdateWorker.this.doWork(this);
        }
    }

    /* compiled from: WidgetUpdateWorker.kt */
    @Metadata(k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.worker.WidgetUpdateWorker", f = "WidgetUpdateWorker.kt", i = {0, 0, 0, 0, 0, 0}, l = {233}, m = "processFramesLoop", n = {"this", "appWidgetManager", "frames", "appWidgetId", "frameDuration", CmcdHeadersFactory.OBJECT_TYPE_INIT_SEGMENT}, s = {"L$0", "L$1", "L$2", "I$0", "J$0", "I$1"})
    /* renamed from: com.proxy.videowidget.domain.worker.WidgetUpdateWorker$processFramesLoop$1, reason: invalid class name and case insensitive filesystem */
    static final class C01061 extends ContinuationImpl {
        int I$0;
        int I$1;
        int I$2;
        long J$0;
        Object L$0;
        Object L$1;
        Object L$2;
        int label;
        /* synthetic */ Object result;

        C01061(Continuation<? super C01061> continuation) {
            super(continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            this.result = obj;
            this.label |= Integer.MIN_VALUE;
            return WidgetUpdateWorker.this.processFramesLoop(0, null, 0.0f, 0, this);
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public WidgetUpdateWorker(Context context, WorkerParameters workerParams) {
        super(context, workerParams);
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(workerParams, "workerParams");
    }

    @Override // androidx.work.CoroutineWorker
    public Object getForegroundInfo(Continuation<? super ForegroundInfo> continuation) {
        return createForegroundInfo();
    }

    /* JADX WARN: Removed duplicated region for block: B:7:0x0014  */
    @Override // androidx.work.CoroutineWorker
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Object doWork(kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> r9) {
        /*
            r8 = this;
            boolean r0 = r9 instanceof com.proxy.videowidget.domain.worker.WidgetUpdateWorker.AnonymousClass1
            if (r0 == 0) goto L14
            r0 = r9
            com.proxy.videowidget.domain.worker.WidgetUpdateWorker$doWork$1 r0 = (com.proxy.videowidget.domain.worker.WidgetUpdateWorker.AnonymousClass1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r1 = r1 & r2
            if (r1 == 0) goto L14
            int r9 = r0.label
            int r9 = r9 - r2
            r0.label = r9
            goto L19
        L14:
            com.proxy.videowidget.domain.worker.WidgetUpdateWorker$doWork$1 r0 = new com.proxy.videowidget.domain.worker.WidgetUpdateWorker$doWork$1
            r0.<init>(r9)
        L19:
            r6 = r0
            java.lang.Object r9 = r6.result
            java.lang.Object r0 = kotlin.coroutines.intrinsics.IntrinsicsKt.getCOROUTINE_SUSPENDED()
            int r1 = r6.label
            r2 = 1
            if (r1 == 0) goto L33
            if (r1 != r2) goto L2b
            kotlin.ResultKt.throwOnFailure(r9)
            goto L8a
        L2b:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r0)
            throw r9
        L33:
            kotlin.ResultKt.throwOnFailure(r9)
            androidx.work.Data r9 = r8.getInputData()
            java.lang.String r1 = "appWidgetId"
            r3 = -1
            int r9 = r9.getInt(r1, r3)
            androidx.work.Data r1 = r8.getInputData()
            java.lang.String r4 = "mediaType"
            java.lang.String r1 = r1.getString(r4)
            if (r1 != 0) goto L4f
            java.lang.String r1 = "video"
        L4f:
            r4 = r1
            androidx.work.Data r1 = r8.getInputData()
            java.lang.String r5 = "inputPath"
            java.lang.String r1 = r1.getString(r5)
            if (r1 != 0) goto L5e
            java.lang.String r1 = ""
        L5e:
            r5 = r1
            androidx.work.Data r1 = r8.getInputData()
            java.lang.String r7 = "framesDirectory"
            java.lang.String r7 = r1.getString(r7)
            if (r9 != r3) goto L7c
            java.lang.String r9 = "WidgetUpdateWorker"
            java.lang.String r0 = "Invalid widget ID provided."
            android.util.Log.e(r9, r0)
            androidx.work.ListenableWorker$Result r9 = androidx.work.ListenableWorker.Result.failure()
            java.lang.String r0 = "failure(...)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r9, r0)
            return r9
        L7c:
            r6.label = r2
            r1 = r8
            r2 = r9
            r3 = r4
            r4 = r5
            r5 = r7
            java.lang.Object r9 = r1.processFrames(r2, r3, r4, r5, r6)
            if (r9 != r0) goto L8a
            return r0
        L8a:
            java.lang.Boolean r9 = (java.lang.Boolean) r9
            boolean r9 = r9.booleanValue()
            if (r9 == 0) goto L99
            androidx.work.ListenableWorker$Result r9 = androidx.work.ListenableWorker.Result.success()
            java.lang.String r0 = "success(...)"
            goto L9f
        L99:
            androidx.work.ListenableWorker$Result r9 = androidx.work.ListenableWorker.Result.retry()
            java.lang.String r0 = "retry(...)"
        L9f:
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r9, r0)
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.worker.WidgetUpdateWorker.doWork(kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final Object processFrames(int i, String str, String str2, String str3, Continuation<? super Boolean> continuation) {
        return BuildersKt.withContext(Dispatchers.getIO(), new AnonymousClass2(str, str2, str3, this, i, AppWidgetManager.getInstance(getApplicationContext()), null), continuation);
    }

    /* compiled from: WidgetUpdateWorker.kt */
    @Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"}, d2 = {"<anonymous>", "", "Lkotlinx/coroutines/CoroutineScope;"}, k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.worker.WidgetUpdateWorker$processFrames$2", f = "WidgetUpdateWorker.kt", i = {}, l = {97, 179}, m = "invokeSuspend", n = {}, s = {})
    /* renamed from: com.proxy.videowidget.domain.worker.WidgetUpdateWorker$processFrames$2, reason: invalid class name */
    static final class AnonymousClass2 extends SuspendLambda implements Function2<CoroutineScope, Continuation<? super Boolean>, Object> {
        final /* synthetic */ int $appWidgetId;
        final /* synthetic */ AppWidgetManager $appWidgetManager;
        final /* synthetic */ String $framesDirectory;
        final /* synthetic */ String $inputPath;
        final /* synthetic */ String $mediaType;
        int label;
        final /* synthetic */ WidgetUpdateWorker this$0;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        AnonymousClass2(String str, String str2, String str3, WidgetUpdateWorker widgetUpdateWorker, int i, AppWidgetManager appWidgetManager, Continuation<? super AnonymousClass2> continuation) {
            super(2, continuation);
            this.$mediaType = str;
            this.$inputPath = str2;
            this.$framesDirectory = str3;
            this.this$0 = widgetUpdateWorker;
            this.$appWidgetId = i;
            this.$appWidgetManager = appWidgetManager;
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Continuation<Unit> create(Object obj, Continuation<?> continuation) {
            return new AnonymousClass2(this.$mediaType, this.$inputPath, this.$framesDirectory, this.this$0, this.$appWidgetId, this.$appWidgetManager, continuation);
        }

        @Override // kotlin.jvm.functions.Function2
        public final Object invoke(CoroutineScope coroutineScope, Continuation<? super Boolean> continuation) {
            return ((AnonymousClass2) create(coroutineScope, continuation)).invokeSuspend(Unit.INSTANCE);
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            File file;
            Object coroutine_suspended = IntrinsicsKt.getCOROUTINE_SUSPENDED();
            int i = this.label;
            boolean zBooleanValue = false;
            boolean zBooleanValue2 = true;
            try {
            } catch (Exception e) {
                Log.e("WidgetUpdateWorker", "Frame extraction failed", e);
            }
            if (i == 0) {
                ResultKt.throwOnFailure(obj);
                if (Intrinsics.areEqual(this.$mediaType, "image")) {
                    Bitmap bitmapDecodeFile = BitmapFactory.decodeFile(this.$inputPath);
                    if (bitmapDecodeFile != null) {
                        WidgetUpdateWorker widgetUpdateWorker = this.this$0;
                        AppWidgetManager appWidgetManager = this.$appWidgetManager;
                        int i2 = this.$appWidgetId;
                        VideoWidget.Companion companion = VideoWidget.INSTANCE;
                        Context applicationContext = widgetUpdateWorker.getApplicationContext();
                        Intrinsics.checkNotNullExpressionValue(applicationContext, "getApplicationContext(...)");
                        Intrinsics.checkNotNull(appWidgetManager);
                        companion.updateAppWidget(applicationContext, appWidgetManager, i2, bitmapDecodeFile);
                    }
                } else {
                    String str = this.$framesDirectory;
                    if (str != null && str.length() != 0) {
                        file = new File(this.$framesDirectory);
                    } else {
                        file = new File(this.this$0.getApplicationContext().getFilesDir(), "frames_" + this.$appWidgetId);
                    }
                    final File file2 = file;
                    Widget widget = null;
                    if (file2.exists() && file2.isDirectory()) {
                        List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
                        if (value != null) {
                            int i3 = this.$appWidgetId;
                            Iterator<T> it = value.iterator();
                            while (true) {
                                if (!it.hasNext()) {
                                    break;
                                }
                                Object next = it.next();
                                if (((Widget) next).getId() == i3) {
                                    widget = next;
                                    break;
                                }
                            }
                            widget = widget;
                        }
                        float framerate = widget != null ? widget.getFramerate() : 30.0f;
                        int iLoadCurrentFrameIndex = this.this$0.loadCurrentFrameIndex(this.$appWidgetId);
                        this.label = 1;
                        obj = this.this$0.processFramesLoop(this.$appWidgetId, file2, framerate, iLoadCurrentFrameIndex, this);
                        if (obj == coroutine_suspended) {
                            return coroutine_suspended;
                        }
                        zBooleanValue2 = ((Boolean) obj).booleanValue();
                    } else {
                        File file3 = new File(this.$inputPath);
                        if (!file3.exists()) {
                            Log.e("WidgetUpdateWorker", "Input file does not exist: " + this.$inputPath);
                            return Boxing.boxBoolean(false);
                        }
                        if (!file2.exists()) {
                            file2.mkdirs();
                        }
                        String str2 = this.$mediaType;
                        if (Intrinsics.areEqual(str2, "video")) {
                            final WidgetUpdateWorker widgetUpdateWorker2 = this.this$0;
                            final int i4 = this.$appWidgetId;
                            final String str3 = this.$mediaType;
                            FrameExtractor frameExtractor = new FrameExtractor(new IVideoFrameExtractor() { // from class: com.proxy.videowidget.domain.worker.WidgetUpdateWorker$processFrames$2$frameExtractor$1
                                @Override // com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor
                                public void onCurrentFrameExtracted(Frame currentFrame) {
                                    Intrinsics.checkNotNullParameter(currentFrame, "currentFrame");
                                }

                                @Override // com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor
                                public void onAllFrameExtracted(int processedFrameCount, long processedTimeMs, float frameRate) {
                                    widgetUpdateWorker2.getApplicationContext().getSharedPreferences("VideoWidgetPrefs", 0).edit().putBoolean(MainActivity.KEY_WIDGET_CREATED, true).apply();
                                    WidgetRepository.INSTANCE.addOrUpdateWidget(new Widget(i4, "Widget " + i4, "Description for widget " + i4, null, file2.getAbsolutePath(), frameRate, str3));
                                }
                            });
                            String absolutePath = file3.getAbsolutePath();
                            Intrinsics.checkNotNullExpressionValue(absolutePath, "getAbsolutePath(...)");
                            String absolutePath2 = file2.getAbsolutePath();
                            Intrinsics.checkNotNullExpressionValue(absolutePath2, "getAbsolutePath(...)");
                            frameExtractor.extractFrames(absolutePath, absolutePath2);
                        } else if (Intrinsics.areEqual(str2, "gif")) {
                            final WidgetUpdateWorker widgetUpdateWorker3 = this.this$0;
                            final int i5 = this.$appWidgetId;
                            final String str4 = this.$mediaType;
                            GifFrameExtractor gifFrameExtractor = new GifFrameExtractor(new IVideoFrameExtractor() { // from class: com.proxy.videowidget.domain.worker.WidgetUpdateWorker$processFrames$2$gifFrameExtractor$1
                                @Override // com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor
                                public void onCurrentFrameExtracted(Frame currentFrame) {
                                    Intrinsics.checkNotNullParameter(currentFrame, "currentFrame");
                                }

                                @Override // com.proxy.videowidget.domain.util.decoder.IVideoFrameExtractor
                                public void onAllFrameExtracted(int processedFrameCount, long processedTimeMs, float frameRate) {
                                    widgetUpdateWorker3.getApplicationContext().getSharedPreferences("VideoWidgetPrefs", 0).edit().putBoolean(MainActivity.KEY_WIDGET_CREATED, true).apply();
                                    WidgetRepository.INSTANCE.addOrUpdateWidget(new Widget(i5, "Widget " + i5, "Description for widget " + i5, null, file2.getAbsolutePath(), frameRate, str4));
                                }
                            });
                            String absolutePath3 = file3.getAbsolutePath();
                            Intrinsics.checkNotNullExpressionValue(absolutePath3, "getAbsolutePath(...)");
                            String absolutePath4 = file2.getAbsolutePath();
                            Intrinsics.checkNotNullExpressionValue(absolutePath4, "getAbsolutePath(...)");
                            gifFrameExtractor.extractFrames(absolutePath3, absolutePath4);
                        }
                        List<Widget> value2 = WidgetRepository.INSTANCE.getWidgets().getValue();
                        if (value2 != null) {
                            int i6 = this.$appWidgetId;
                            Iterator<T> it2 = value2.iterator();
                            while (true) {
                                if (!it2.hasNext()) {
                                    break;
                                }
                                Object next2 = it2.next();
                                if (((Widget) next2).getId() == i6) {
                                    widget = next2;
                                    break;
                                }
                            }
                            widget = widget;
                        }
                        float framerate2 = widget != null ? widget.getFramerate() : 30.0f;
                        this.label = 2;
                        obj = this.this$0.processFramesLoop(this.$appWidgetId, file2, framerate2, 0, this);
                        if (obj == coroutine_suspended) {
                            return coroutine_suspended;
                        }
                        zBooleanValue = ((Boolean) obj).booleanValue();
                        zBooleanValue2 = zBooleanValue;
                    }
                }
            } else if (i == 1) {
                ResultKt.throwOnFailure(obj);
                zBooleanValue2 = ((Boolean) obj).booleanValue();
            } else {
                if (i != 2) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                ResultKt.throwOnFailure(obj);
                zBooleanValue = ((Boolean) obj).booleanValue();
                zBooleanValue2 = zBooleanValue;
            }
            return Boxing.boxBoolean(zBooleanValue2);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0097  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x00ae  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x0160  */
    /* JADX WARN: Removed duplicated region for block: B:47:0x016f  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0018  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:25:0x0097 -> B:26:0x00ac). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:33:0x00cd -> B:45:0x015b). Please report as a decompilation issue!!! */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:43:0x0146 -> B:44:0x0152). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final java.lang.Object processFramesLoop(int r20, java.io.File r21, float r22, int r23, kotlin.coroutines.Continuation<? super java.lang.Boolean> r24) {
        /*
            Method dump skipped, instructions count: 379
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.worker.WidgetUpdateWorker.processFramesLoop(int, java.io.File, float, int, kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final boolean processFramesLoop$lambda$0(File file) {
        String name = file.getName();
        Intrinsics.checkNotNullExpressionValue(name, "getName(...)");
        return StringsKt.endsWith$default(name, ".jpeg", false, 2, (Object) null);
    }

    private final void saveCurrentFrameIndex(int appWidgetId, int index) {
        getApplicationContext().getSharedPreferences("WidgetFramePrefs", 0).edit().putInt("widget_" + appWidgetId, index).apply();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final int loadCurrentFrameIndex(int appWidgetId) {
        return getApplicationContext().getSharedPreferences("WidgetFramePrefs", 0).getInt("widget_" + appWidgetId, 0);
    }

    private final ForegroundInfo createForegroundInfo() {
        Notification notificationCreateNotification = createNotification();
        if (Build.VERSION.SDK_INT >= 29) {
            return new ForegroundInfo(2, notificationCreateNotification, 2);
        }
        return new ForegroundInfo(2, notificationCreateNotification);
    }

    private final Notification createNotification() {
        if (Build.VERSION.SDK_INT >= 26) {
            ((NotificationManager) getApplicationContext().getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel(CHANNEL_ID, "Widget Update", 2));
        }
        Notification notificationBuild = new NotificationCompat.Builder(getApplicationContext(), CHANNEL_ID).setContentTitle("Updating Widget").setSmallIcon(R.drawable.ic_logo).setOngoing(true).build();
        Intrinsics.checkNotNullExpressionValue(notificationBuild, "build(...)");
        return notificationBuild;
    }
}
