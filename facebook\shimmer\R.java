package com.facebook.shimmer;

/* loaded from: classes.dex */
public final class R {

    public static final class attr {
        public static int shimmer_auto_start = 0x7f0403f7;
        public static int shimmer_base_alpha = 0x7f0403f8;
        public static int shimmer_base_color = 0x7f0403f9;
        public static int shimmer_clip_to_children = 0x7f0403fa;
        public static int shimmer_colored = 0x7f0403fb;
        public static int shimmer_direction = 0x7f0403fc;
        public static int shimmer_dropoff = 0x7f0403fd;
        public static int shimmer_duration = 0x7f0403fe;
        public static int shimmer_fixed_height = 0x7f0403ff;
        public static int shimmer_fixed_width = 0x7f040400;
        public static int shimmer_height_ratio = 0x7f040401;
        public static int shimmer_highlight_alpha = 0x7f040402;
        public static int shimmer_highlight_color = 0x7f040403;
        public static int shimmer_intensity = 0x7f040404;
        public static int shimmer_repeat_count = 0x7f040405;
        public static int shimmer_repeat_delay = 0x7f040406;
        public static int shimmer_repeat_mode = 0x7f040407;
        public static int shimmer_shape = 0x7f040408;
        public static int shimmer_tilt = 0x7f040409;
        public static int shimmer_width_ratio = 0x7f04040a;

        private attr() {
        }
    }

    public static final class id {
        public static int bottom_to_top = 0x7f0a006c;
        public static int left_to_right = 0x7f0a014f;
        public static int linear = 0x7f0a015a;
        public static int radial = 0x7f0a01e7;
        public static int restart = 0x7f0a01f1;
        public static int reverse = 0x7f0a01f2;
        public static int right_to_left = 0x7f0a01f8;
        public static int top_to_bottom = 0x7f0a0274;

        private id() {
        }
    }

    public static final class styleable {
        public static int[] ShimmerFrameLayout = {com.proxy.videowidget.R.attr.shimmer_auto_start, com.proxy.videowidget.R.attr.shimmer_base_alpha, com.proxy.videowidget.R.attr.shimmer_base_color, com.proxy.videowidget.R.attr.shimmer_clip_to_children, com.proxy.videowidget.R.attr.shimmer_colored, com.proxy.videowidget.R.attr.shimmer_direction, com.proxy.videowidget.R.attr.shimmer_dropoff, com.proxy.videowidget.R.attr.shimmer_duration, com.proxy.videowidget.R.attr.shimmer_fixed_height, com.proxy.videowidget.R.attr.shimmer_fixed_width, com.proxy.videowidget.R.attr.shimmer_height_ratio, com.proxy.videowidget.R.attr.shimmer_highlight_alpha, com.proxy.videowidget.R.attr.shimmer_highlight_color, com.proxy.videowidget.R.attr.shimmer_intensity, com.proxy.videowidget.R.attr.shimmer_repeat_count, com.proxy.videowidget.R.attr.shimmer_repeat_delay, com.proxy.videowidget.R.attr.shimmer_repeat_mode, com.proxy.videowidget.R.attr.shimmer_shape, com.proxy.videowidget.R.attr.shimmer_tilt, com.proxy.videowidget.R.attr.shimmer_width_ratio};
        public static int ShimmerFrameLayout_shimmer_auto_start = 0x00000000;
        public static int ShimmerFrameLayout_shimmer_base_alpha = 0x00000001;
        public static int ShimmerFrameLayout_shimmer_base_color = 0x00000002;
        public static int ShimmerFrameLayout_shimmer_clip_to_children = 0x00000003;
        public static int ShimmerFrameLayout_shimmer_colored = 0x00000004;
        public static int ShimmerFrameLayout_shimmer_direction = 0x00000005;
        public static int ShimmerFrameLayout_shimmer_dropoff = 0x00000006;
        public static int ShimmerFrameLayout_shimmer_duration = 0x00000007;
        public static int ShimmerFrameLayout_shimmer_fixed_height = 0x00000008;
        public static int ShimmerFrameLayout_shimmer_fixed_width = 0x00000009;
        public static int ShimmerFrameLayout_shimmer_height_ratio = 0x0000000a;
        public static int ShimmerFrameLayout_shimmer_highlight_alpha = 0x0000000b;
        public static int ShimmerFrameLayout_shimmer_highlight_color = 0x0000000c;
        public static int ShimmerFrameLayout_shimmer_intensity = 0x0000000d;
        public static int ShimmerFrameLayout_shimmer_repeat_count = 0x0000000e;
        public static int ShimmerFrameLayout_shimmer_repeat_delay = 0x0000000f;
        public static int ShimmerFrameLayout_shimmer_repeat_mode = 0x00000010;
        public static int ShimmerFrameLayout_shimmer_shape = 0x00000011;
        public static int ShimmerFrameLayout_shimmer_tilt = 0x00000012;
        public static int ShimmerFrameLayout_shimmer_width_ratio = 0x00000013;

        private styleable() {
        }
    }

    private R() {
    }
}
