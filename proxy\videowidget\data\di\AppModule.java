package com.proxy.videowidget.data.di;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.appwidget.AppWidgetManager;
import android.content.Context;
import android.os.Build;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import com.proxy.videowidget.R;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.android.qualifiers.ApplicationContext;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.inject.Singleton;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: AppModule.kt */
@Metadata(d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bÇ\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0002J\u0012\u0010\n\u001a\u00020\u000b2\b\b\u0001\u0010\b\u001a\u00020\tH\u0007J\b\u0010\f\u001a\u00020\rH\u0007J\u0012\u0010\u000e\u001a\u00020\u000f2\b\b\u0001\u0010\b\u001a\u00020\tH\u0007J\u0012\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\b\u001a\u00020\tH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u0012"}, d2 = {"Lcom/proxy/videowidget/data/di/AppModule;", "", "()V", "NOTIFICATION_CHANNEL_ID", "", "NOTIFICATION_CHANNEL_NAME", "createNotificationChannel", "", "context", "Landroid/content/Context;", "provideAppWidgetManager", "Landroid/appwidget/AppWidgetManager;", "provideExecutorService", "Ljava/util/concurrent/ExecutorService;", "provideNotificationBuilder", "Landroidx/core/app/NotificationCompat$Builder;", "provideNotificationManager", "Landroid/app/NotificationManager;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@Module
/* loaded from: classes3.dex */
public final class AppModule {
    public static final AppModule INSTANCE = new AppModule();
    private static final String NOTIFICATION_CHANNEL_ID = "running_text_channel";
    private static final String NOTIFICATION_CHANNEL_NAME = "Running Text Channel";

    private AppModule() {
    }

    @Provides
    @Singleton
    public final ExecutorService provideExecutorService() {
        ExecutorService executorServiceNewFixedThreadPool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        Intrinsics.checkNotNullExpressionValue(executorServiceNewFixedThreadPool, "newFixedThreadPool(...)");
        return executorServiceNewFixedThreadPool;
    }

    @Provides
    @Singleton
    public final AppWidgetManager provideAppWidgetManager(@ApplicationContext Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
        Intrinsics.checkNotNullExpressionValue(appWidgetManager, "getInstance(...)");
        return appWidgetManager;
    }

    @Provides
    @Singleton
    public final NotificationManager provideNotificationManager(@ApplicationContext Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Object systemService = context.getSystemService("notification");
        Intrinsics.checkNotNull(systemService, "null cannot be cast to non-null type android.app.NotificationManager");
        return (NotificationManager) systemService;
    }

    @Provides
    @Singleton
    public final NotificationCompat.Builder provideNotificationBuilder(@ApplicationContext Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        createNotificationChannel(context);
        NotificationCompat.Builder priority = new NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID).setSmallIcon(R.drawable.ic_notification).setContentTitle("Running Video Widget").setContentText("Video Widget Running").setPriority(0);
        Intrinsics.checkNotNullExpressionValue(priority, "setPriority(...)");
        return priority;
    }

    private final void createNotificationChannel(Context context) {
        if (Build.VERSION.SDK_INT >= 26) {
            NotificationChannel notificationChannel = new NotificationChannel(NOTIFICATION_CHANNEL_ID, NOTIFICATION_CHANNEL_NAME, 3);
            notificationChannel.setDescription("Channel for running video widget notifications");
            ((NotificationManager) context.getSystemService(NotificationManager.class)).createNotificationChannel(notificationChannel);
        }
    }
}
