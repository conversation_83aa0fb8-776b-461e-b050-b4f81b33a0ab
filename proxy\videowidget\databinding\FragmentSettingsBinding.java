package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentSettingsBinding implements ViewBinding {
    public final TextView aboutHeader;
    public final LinearLayout aboutSettings;
    public final MaterialButton backBtn;
    public final LinearLayout generalSettings;
    public final TextView generalSettingsHeader;
    public final TextView headerText;
    public final TextView legalHeader;
    public final LinearLayout legalSettings;
    public final LinearLayout notificationSettings;
    public final SwitchCompat notificationSwitch;
    public final TextView notificationsText;
    public final ImageView privacyIcon;
    public final TextView privacyPolicy;
    private final ConstraintLayout rootView;
    public final RadioButton themeAuto;
    public final RadioButton themeDark;
    public final RadioButton themeLight;
    public final RadioGroup themeOptions;
    public final LinearLayout themeSettings;
    public final TextView themeText;
    public final TextView tos;
    public final ImageView tosIcon;
    public final TextView versionInfo;

    private FragmentSettingsBinding(ConstraintLayout constraintLayout, TextView textView, LinearLayout linearLayout, MaterialButton materialButton, LinearLayout linearLayout2, TextView textView2, TextView textView3, TextView textView4, LinearLayout linearLayout3, LinearLayout linearLayout4, SwitchCompat switchCompat, TextView textView5, ImageView imageView, TextView textView6, RadioButton radioButton, RadioButton radioButton2, RadioButton radioButton3, RadioGroup radioGroup, LinearLayout linearLayout5, TextView textView7, TextView textView8, ImageView imageView2, TextView textView9) {
        this.rootView = constraintLayout;
        this.aboutHeader = textView;
        this.aboutSettings = linearLayout;
        this.backBtn = materialButton;
        this.generalSettings = linearLayout2;
        this.generalSettingsHeader = textView2;
        this.headerText = textView3;
        this.legalHeader = textView4;
        this.legalSettings = linearLayout3;
        this.notificationSettings = linearLayout4;
        this.notificationSwitch = switchCompat;
        this.notificationsText = textView5;
        this.privacyIcon = imageView;
        this.privacyPolicy = textView6;
        this.themeAuto = radioButton;
        this.themeDark = radioButton2;
        this.themeLight = radioButton3;
        this.themeOptions = radioGroup;
        this.themeSettings = linearLayout5;
        this.themeText = textView7;
        this.tos = textView8;
        this.tosIcon = imageView2;
        this.versionInfo = textView9;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentSettingsBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentSettingsBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_settings, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentSettingsBinding bind(View view) {
        int i = R.id.aboutHeader;
        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
        if (textView != null) {
            i = R.id.aboutSettings;
            LinearLayout linearLayout = (LinearLayout) ViewBindings.findChildViewById(view, i);
            if (linearLayout != null) {
                i = R.id.backBtn;
                MaterialButton materialButton = (MaterialButton) ViewBindings.findChildViewById(view, i);
                if (materialButton != null) {
                    i = R.id.generalSettings;
                    LinearLayout linearLayout2 = (LinearLayout) ViewBindings.findChildViewById(view, i);
                    if (linearLayout2 != null) {
                        i = R.id.generalSettingsHeader;
                        TextView textView2 = (TextView) ViewBindings.findChildViewById(view, i);
                        if (textView2 != null) {
                            i = R.id.headerText;
                            TextView textView3 = (TextView) ViewBindings.findChildViewById(view, i);
                            if (textView3 != null) {
                                i = R.id.legalHeader;
                                TextView textView4 = (TextView) ViewBindings.findChildViewById(view, i);
                                if (textView4 != null) {
                                    i = R.id.legalSettings;
                                    LinearLayout linearLayout3 = (LinearLayout) ViewBindings.findChildViewById(view, i);
                                    if (linearLayout3 != null) {
                                        i = R.id.notificationSettings;
                                        LinearLayout linearLayout4 = (LinearLayout) ViewBindings.findChildViewById(view, i);
                                        if (linearLayout4 != null) {
                                            i = R.id.notificationSwitch;
                                            SwitchCompat switchCompat = (SwitchCompat) ViewBindings.findChildViewById(view, i);
                                            if (switchCompat != null) {
                                                i = R.id.notificationsText;
                                                TextView textView5 = (TextView) ViewBindings.findChildViewById(view, i);
                                                if (textView5 != null) {
                                                    i = R.id.privacyIcon;
                                                    ImageView imageView = (ImageView) ViewBindings.findChildViewById(view, i);
                                                    if (imageView != null) {
                                                        i = R.id.privacyPolicy;
                                                        TextView textView6 = (TextView) ViewBindings.findChildViewById(view, i);
                                                        if (textView6 != null) {
                                                            i = R.id.themeAuto;
                                                            RadioButton radioButton = (RadioButton) ViewBindings.findChildViewById(view, i);
                                                            if (radioButton != null) {
                                                                i = R.id.themeDark;
                                                                RadioButton radioButton2 = (RadioButton) ViewBindings.findChildViewById(view, i);
                                                                if (radioButton2 != null) {
                                                                    i = R.id.themeLight;
                                                                    RadioButton radioButton3 = (RadioButton) ViewBindings.findChildViewById(view, i);
                                                                    if (radioButton3 != null) {
                                                                        i = R.id.themeOptions;
                                                                        RadioGroup radioGroup = (RadioGroup) ViewBindings.findChildViewById(view, i);
                                                                        if (radioGroup != null) {
                                                                            i = R.id.themeSettings;
                                                                            LinearLayout linearLayout5 = (LinearLayout) ViewBindings.findChildViewById(view, i);
                                                                            if (linearLayout5 != null) {
                                                                                i = R.id.themeText;
                                                                                TextView textView7 = (TextView) ViewBindings.findChildViewById(view, i);
                                                                                if (textView7 != null) {
                                                                                    i = R.id.tos;
                                                                                    TextView textView8 = (TextView) ViewBindings.findChildViewById(view, i);
                                                                                    if (textView8 != null) {
                                                                                        i = R.id.tosIcon;
                                                                                        ImageView imageView2 = (ImageView) ViewBindings.findChildViewById(view, i);
                                                                                        if (imageView2 != null) {
                                                                                            i = R.id.versionInfo;
                                                                                            TextView textView9 = (TextView) ViewBindings.findChildViewById(view, i);
                                                                                            if (textView9 != null) {
                                                                                                return new FragmentSettingsBinding((ConstraintLayout) view, textView, linearLayout, materialButton, linearLayout2, textView2, textView3, textView4, linearLayout3, linearLayout4, switchCompat, textView5, imageView, textView6, radioButton, radioButton2, radioButton3, radioGroup, linearLayout5, textView7, textView8, imageView2, textView9);
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
