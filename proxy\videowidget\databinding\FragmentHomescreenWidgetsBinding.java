package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentHomescreenWidgetsBinding implements ViewBinding {
    public final TextView emptyListText;
    public final RecyclerView recyclerViewWidgets;
    private final ConstraintLayout rootView;

    private FragmentHomescreenWidgetsBinding(ConstraintLayout constraintLayout, TextView textView, RecyclerView recyclerView) {
        this.rootView = constraintLayout;
        this.emptyListText = textView;
        this.recyclerViewWidgets = recyclerView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentHomescreenWidgetsBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentHomescreenWidgetsBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_homescreen_widgets, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentHomescreenWidgetsBinding bind(View view) {
        int i = R.id.emptyListText;
        TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
        if (textView != null) {
            i = R.id.recyclerViewWidgets;
            RecyclerView recyclerView = (RecyclerView) ViewBindings.findChildViewById(view, i);
            if (recyclerView != null) {
                return new FragmentHomescreenWidgetsBinding((ConstraintLayout) view, textView, recyclerView);
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
