package com.proxy.videowidget.presentation.widget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Build;
import android.util.Log;
import android.widget.RemoteViews;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.OutOfQuotaPolicy;
import androidx.work.WorkManager;
import com.proxy.videowidget.R;
import com.proxy.videowidget.domain.service.WidgetService;
import com.proxy.videowidget.domain.worker.WidgetUpdateWorker;
import com.proxy.videowidget.repository.WidgetRepository;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.Iterator;
import java.util.List;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.TuplesKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: VideoWidget.kt */
@Metadata(d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0015\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u0005¢\u0006\u0002\u0010\u0002J,\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\nH\u0002J\u0018\u0010\f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0010\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0018\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J \u0010\u0014\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\r\u001a\u00020\u000eH\u0016¨\u0006\u0018"}, d2 = {"Lcom/proxy/videowidget/presentation/widget/VideoWidget;", "Landroid/appwidget/AppWidgetProvider;", "()V", "enqueueWidgetUpdateWork", "", "context", "Landroid/content/Context;", "appWidgetId", "", "framesDirectory", "", "mediaType", "onDeleted", "appWidgetIds", "", "onDisabled", "onEnabled", "onReceive", "intent", "Landroid/content/Intent;", "onUpdate", "appWidgetManager", "Landroid/appwidget/AppWidgetManager;", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@AndroidEntryPoint
/* loaded from: classes3.dex */
public final class VideoWidget extends Hilt_VideoWidget {
    public static final String ACTION_WIDGET_TAP = "com.proxy.videowidget.ACTION_WIDGET_TAP";

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = new Companion(null);
    private static final String TAG = "VideoWidget";

    /* compiled from: VideoWidget.kt */
    @Metadata(d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u0018\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\nH\u0002J(\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u0014"}, d2 = {"Lcom/proxy/videowidget/presentation/widget/VideoWidget$Companion;", "", "()V", "ACTION_WIDGET_TAP", "", "TAG", "getRoundedCornerBitmap", "Landroid/graphics/Bitmap;", "bitmap", "cornerRadius", "", "updateAppWidget", "", "context", "Landroid/content/Context;", "appWidgetManager", "Landroid/appwidget/AppWidgetManager;", "appWidgetId", "", "imageBitmap", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        /* JADX WARN: Multi-variable type inference failed */
        public final void updateAppWidget(Context context, AppWidgetManager appWidgetManager, int appWidgetId, Bitmap imageBitmap) {
            String mediaType;
            Intrinsics.checkNotNullParameter(context, "context");
            Intrinsics.checkNotNullParameter(appWidgetManager, "appWidgetManager");
            List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
            Widget widget = null;
            if (value != null) {
                Iterator<T> it = value.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    Object next = it.next();
                    if (((Widget) next).getId() == appWidgetId) {
                        widget = next;
                        break;
                    }
                }
                widget = widget;
            }
            if (widget == null || (mediaType = widget.getMediaType()) == null) {
                mediaType = "video";
            }
            RemoteViews remoteViews = new RemoteViews(context.getPackageName(), R.layout.video_widget);
            if (imageBitmap == null && Intrinsics.areEqual(mediaType, "image")) {
                imageBitmap = WidgetRepository.INSTANCE.loadImageBitmapFromFile(appWidgetId);
            }
            if (imageBitmap != null) {
                remoteViews.setImageViewBitmap(R.id.imageVideoView, getRoundedCornerBitmap(imageBitmap, 20.0f));
                remoteViews.setViewVisibility(R.id.progressBar, 8);
            } else {
                remoteViews.setViewVisibility(R.id.progressBar, 0);
            }
            if (!Intrinsics.areEqual(mediaType, "image")) {
                Intent intent = new Intent(context, (Class<?>) VideoWidget.class);
                intent.setAction(VideoWidget.ACTION_WIDGET_TAP);
                intent.putExtra("appWidgetId", appWidgetId);
                remoteViews.setOnClickPendingIntent(R.id.widget_container, PendingIntent.getBroadcast(context, appWidgetId, intent, 201326592));
            }
            appWidgetManager.updateAppWidget(appWidgetId, remoteViews);
        }

        private final Bitmap getRoundedCornerBitmap(Bitmap bitmap, float cornerRadius) {
            Bitmap bitmapCreateBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Bitmap.Config.ARGB_8888);
            Intrinsics.checkNotNullExpressionValue(bitmapCreateBitmap, "createBitmap(...)");
            Canvas canvas = new Canvas(bitmapCreateBitmap);
            Paint paint = new Paint();
            Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
            RectF rectF = new RectF(rect);
            paint.setAntiAlias(true);
            canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint);
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
            canvas.drawBitmap(bitmap, rect, rect, paint);
            return bitmapCreateBitmap;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.proxy.videowidget.presentation.widget.Hilt_VideoWidget, android.appwidget.AppWidgetProvider, android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        int intExtra;
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(intent, "intent");
        super.onReceive(context, intent);
        WidgetRepository.INSTANCE.initialize(context);
        if (!Intrinsics.areEqual(intent.getAction(), ACTION_WIDGET_TAP) || (intExtra = intent.getIntExtra("appWidgetId", -1)) == -1) {
            return;
        }
        List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
        Widget widget = null;
        if (value != null) {
            Iterator<T> it = value.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Object next = it.next();
                if (((Widget) next).getId() == intExtra) {
                    widget = next;
                    break;
                }
            }
            widget = widget;
        }
        if (widget != null) {
            enqueueWidgetUpdateWork(context, intExtra, widget.getFramesDirectory(), widget.getMediaType());
        }
    }

    @Override // android.appwidget.AppWidgetProvider
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        Widget widget;
        Object next;
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(appWidgetManager, "appWidgetManager");
        Intrinsics.checkNotNullParameter(appWidgetIds, "appWidgetIds");
        WidgetRepository.INSTANCE.initialize(context);
        for (int i : appWidgetIds) {
            List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
            if (value != null) {
                Iterator<T> it = value.iterator();
                while (true) {
                    if (it.hasNext()) {
                        next = it.next();
                        if (((Widget) next).getId() == i) {
                            break;
                        }
                    } else {
                        next = null;
                        break;
                    }
                }
                widget = (Widget) next;
            } else {
                widget = null;
            }
            if (widget != null) {
                INSTANCE.updateAppWidget(context, appWidgetManager, i, null);
                String mediaType = widget.getMediaType();
                if (mediaType == null) {
                    mediaType = "video";
                }
                if (!Intrinsics.areEqual(mediaType, "image")) {
                    enqueueWidgetUpdateWork(context, i, widget.getFramesDirectory(), mediaType);
                }
                Log.d(TAG, "OnUpdate: " + i);
            }
        }
    }

    @Override // android.appwidget.AppWidgetProvider
    public void onDeleted(Context context, int[] appWidgetIds) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(appWidgetIds, "appWidgetIds");
        for (int i : appWidgetIds) {
            Intent intent = new Intent(context, (Class<?>) WidgetService.class);
            intent.setAction(WidgetService.ACTION_DELETE_WIDGET);
            intent.putExtra("appWidgetId", i);
            if (Build.VERSION.SDK_INT >= 26) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
            Log.d(TAG, "onDeleted: " + i);
        }
    }

    @Override // android.appwidget.AppWidgetProvider
    public void onEnabled(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Log.d(TAG, "onEnabled");
    }

    @Override // android.appwidget.AppWidgetProvider
    public void onDisabled(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Log.d(TAG, "onDisabled");
    }

    private final void enqueueWidgetUpdateWork(Context context, int appWidgetId, String framesDirectory, String mediaType) {
        Pair[] pairArr = new Pair[3];
        pairArr[0] = TuplesKt.to("appWidgetId", Integer.valueOf(appWidgetId));
        pairArr[1] = TuplesKt.to("framesDirectory", framesDirectory);
        if (mediaType == null) {
            mediaType = "video";
        }
        pairArr[2] = TuplesKt.to("mediaType", mediaType);
        Data.Builder builder = new Data.Builder();
        for (int i = 0; i < 3; i++) {
            Pair pair = pairArr[i];
            builder.put((String) pair.getFirst(), pair.getSecond());
        }
        Data dataBuild = builder.build();
        Intrinsics.checkNotNullExpressionValue(dataBuild, "dataBuilder.build()");
        WorkManager.getInstance(context).enqueueUniqueWork("WidgetUpdate_" + appWidgetId, ExistingWorkPolicy.KEEP, new OneTimeWorkRequest.Builder(WidgetUpdateWorker.class).setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST).setInputData(dataBuild).build());
    }
}
