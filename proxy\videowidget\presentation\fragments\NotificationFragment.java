package com.proxy.videowidget.presentation.fragments;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentViewModelLazyKt;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;
import com.proxy.videowidget.presentation.MainActivity;
import com.proxy.videowidget.presentation.adapter.Notification;
import com.proxy.videowidget.presentation.adapter.NotificationAdapter;
import com.proxy.videowidget.presentation.viewmodel.NotificationViewModel;
import java.util.List;
import kotlin.Lazy;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;

/* compiled from: NotificationFragment.kt */
@Metadata(d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 #2\u00020\u0001:\u0001#B\u0005¢\u0006\u0002\u0010\u0002J\b\u0010\u000f\u001a\u00020\u0010H\u0002J\"\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00132\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0016J&\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010\u001f\u001a\u00020\u0010H\u0002J\u0010\u0010 \u001a\u00020\u00102\u0006\u0010!\u001a\u00020\u0018H\u0002J\b\u0010\"\u001a\u00020\u0010H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.¢\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002¢\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u000e\u0010\r\u001a\u00020\u000eX\u0082.¢\u0006\u0002\n\u0000¨\u0006$"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/NotificationFragment;", "Landroidx/fragment/app/Fragment;", "()V", "adapter", "Lcom/proxy/videowidget/presentation/adapter/NotificationAdapter;", "emptyListText", "Landroid/widget/TextView;", "notificationViewModel", "Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "getNotificationViewModel", "()Lcom/proxy/videowidget/presentation/viewmodel/NotificationViewModel;", "notificationViewModel$delegate", "Lkotlin/Lazy;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "observeNotifications", "", "onActivityResult", "requestCode", "", "resultCode", "data", "Landroid/content/Intent;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", TtmlNode.RUBY_CONTAINER, "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "requestDisableBatteryOptimization", "setupBackButton", "view", "setupRecyclerView", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class NotificationFragment extends Fragment {
    private static final int BATTERY_OPTIMIZATION_REQUEST_CODE = 1001;

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = new Companion(null);
    private NotificationAdapter adapter;
    private TextView emptyListText;

    /* renamed from: notificationViewModel$delegate, reason: from kotlin metadata */
    private final Lazy notificationViewModel;
    private RecyclerView recyclerView;

    public NotificationFragment() {
        final NotificationFragment notificationFragment = this;
        final Function0 function0 = null;
        this.notificationViewModel = FragmentViewModelLazyKt.createViewModelLazy(notificationFragment, Reflection.getOrCreateKotlinClass(NotificationViewModel.class), new Function0<ViewModelStore>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment$special$$inlined$activityViewModels$default$1
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelStore invoke() {
                return notificationFragment.requireActivity().getViewModelStore();
            }
        }, new Function0<CreationExtras>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment$special$$inlined$activityViewModels$default$2
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final CreationExtras invoke() {
                CreationExtras creationExtras;
                Function0 function02 = function0;
                return (function02 == null || (creationExtras = (CreationExtras) function02.invoke()) == null) ? notificationFragment.requireActivity().getDefaultViewModelCreationExtras() : creationExtras;
            }
        }, new Function0<ViewModelProvider.Factory>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment$special$$inlined$activityViewModels$default$3
            {
                super(0);
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function0
            public final ViewModelProvider.Factory invoke() {
                return notificationFragment.requireActivity().getDefaultViewModelProviderFactory();
            }
        });
    }

    private final NotificationViewModel getNotificationViewModel() {
        return (NotificationViewModel) this.notificationViewModel.getValue();
    }

    @Override // androidx.fragment.app.Fragment
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Intrinsics.checkNotNullParameter(inflater, "inflater");
        View viewInflate = inflater.inflate(R.layout.fragment_notification, container, false);
        View viewFindViewById = viewInflate.findViewById(R.id.recyclerViewNotifications);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        this.recyclerView = (RecyclerView) viewFindViewById;
        View viewFindViewById2 = viewInflate.findViewById(R.id.emptyListText);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById2, "findViewById(...)");
        this.emptyListText = (TextView) viewFindViewById2;
        setupRecyclerView();
        Intrinsics.checkNotNull(viewInflate);
        setupBackButton(viewInflate);
        observeNotifications();
        return viewInflate;
    }

    private final void setupRecyclerView() {
        this.adapter = new NotificationAdapter(CollectionsKt.emptyList(), new Function1<Notification, Unit>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment.setupRecyclerView.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(Notification notification) {
                invoke2(notification);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(Notification notification) {
                Intrinsics.checkNotNullParameter(notification, "notification");
                NotificationFragment.this.requestDisableBatteryOptimization();
            }
        });
        RecyclerView recyclerView = this.recyclerView;
        NotificationAdapter notificationAdapter = null;
        if (recyclerView == null) {
            Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
            recyclerView = null;
        }
        NotificationAdapter notificationAdapter2 = this.adapter;
        if (notificationAdapter2 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("adapter");
        } else {
            notificationAdapter = notificationAdapter2;
        }
        recyclerView.setAdapter(notificationAdapter);
    }

    private final void observeNotifications() {
        getNotificationViewModel().getNotifications().observe(getViewLifecycleOwner(), new NotificationFragment$sam$androidx_lifecycle_Observer$0(new Function1<List<? extends Notification>, Unit>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment.observeNotifications.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(List<? extends Notification> list) {
                invoke2((List<Notification>) list);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(List<Notification> list) {
                Log.d("NotificationFragment", "Observed notifications in NotificationFragment: " + list.size());
                NotificationFragment notificationFragment = NotificationFragment.this;
                Intrinsics.checkNotNull(list);
                final NotificationFragment notificationFragment2 = NotificationFragment.this;
                notificationFragment.adapter = new NotificationAdapter(list, new Function1<Notification, Unit>() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment.observeNotifications.1.1
                    {
                        super(1);
                    }

                    @Override // kotlin.jvm.functions.Function1
                    public /* bridge */ /* synthetic */ Unit invoke(Notification notification) {
                        invoke2(notification);
                        return Unit.INSTANCE;
                    }

                    /* renamed from: invoke, reason: avoid collision after fix types in other method */
                    public final void invoke2(Notification notification) {
                        Intrinsics.checkNotNullParameter(notification, "notification");
                        notificationFragment2.requestDisableBatteryOptimization();
                    }
                });
                RecyclerView recyclerView = NotificationFragment.this.recyclerView;
                TextView textView = null;
                if (recyclerView == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("recyclerView");
                    recyclerView = null;
                }
                NotificationAdapter notificationAdapter = NotificationFragment.this.adapter;
                if (notificationAdapter == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("adapter");
                    notificationAdapter = null;
                }
                recyclerView.setAdapter(notificationAdapter);
                TextView textView2 = NotificationFragment.this.emptyListText;
                if (textView2 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("emptyListText");
                } else {
                    textView = textView2;
                }
                textView.setVisibility(list.isEmpty() ? 0 : 8);
            }
        }));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void requestDisableBatteryOptimization() {
        Intent intent = new Intent("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
        intent.setData(Uri.parse("package:" + requireContext().getPackageName()));
        startActivityForResult(intent, 1001);
    }

    private final void setupBackButton(View view) {
        View viewFindViewById = view.findViewById(R.id.backBtn);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        ((MaterialButton) viewFindViewById).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.NotificationFragment$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                NotificationFragment.setupBackButton$lambda$0(this.f$0, view2);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupBackButton$lambda$0(NotificationFragment this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.requireActivity().getSupportFragmentManager().popBackStackImmediate();
    }

    @Override // androidx.fragment.app.Fragment
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1001) {
            FragmentActivity fragmentActivityRequireActivity = requireActivity();
            Intrinsics.checkNotNull(fragmentActivityRequireActivity, "null cannot be cast to non-null type com.proxy.videowidget.presentation.MainActivity");
            ((MainActivity) fragmentActivityRequireActivity).recheckBatteryOptimizationStatus();
        }
    }

    /* compiled from: NotificationFragment.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0006R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u0007"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/NotificationFragment$Companion;", "", "()V", "BATTERY_OPTIMIZATION_REQUEST_CODE", "", "newInstance", "Lcom/proxy/videowidget/presentation/fragments/NotificationFragment;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final NotificationFragment newInstance() {
            return new NotificationFragment();
        }
    }
}
