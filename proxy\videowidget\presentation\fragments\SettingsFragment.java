package com.proxy.videowidget.presentation.fragments;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: SettingsFragment.kt */
@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0002\b\t\u0018\u0000  2\u00020\u0001:\u0001 B\u0005¢\u0006\u0002\u0010\u0002J\u0012\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0016J&\u0010\u000b\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u00102\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0016J\b\u0010\u0011\u001a\u00020\bH\u0016J\b\u0010\u0012\u001a\u00020\bH\u0016J\b\u0010\u0013\u001a\u00020\bH\u0016J\b\u0010\u0014\u001a\u00020\bH\u0016J\b\u0010\u0015\u001a\u00020\bH\u0016J\u0010\u0010\u0016\u001a\u00020\b2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\b\u0010\u0019\u001a\u00020\bH\u0002J\u0010\u0010\u001a\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\fH\u0002J\u0010\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\fH\u0002J\u0010\u0010\u001d\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\fH\u0002J\u0010\u0010\u001e\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\fH\u0002J\u0010\u0010\u001f\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.¢\u0006\u0002\n\u0000¨\u0006!"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/SettingsFragment;", "Landroidx/fragment/app/Fragment;", "()V", "isInitialSetup", "", "sharedPreferences", "Landroid/content/SharedPreferences;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", TtmlNode.RUBY_CONTAINER, "Landroid/view/ViewGroup;", "onDestroy", "onPause", "onResume", "onStart", "onStop", "openWebPage", ImagesContract.URL, "", "restartActivity", "setVersionInfo", "view", "setupBackButton", "setupLegalLinks", "setupThemeSwitch", "updateTheme", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class SettingsFragment extends Fragment {

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = new Companion(null);
    private boolean isInitialSetup = true;
    private SharedPreferences sharedPreferences;

    @Override // androidx.fragment.app.Fragment
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) throws Resources.NotFoundException {
        Intrinsics.checkNotNullParameter(inflater, "inflater");
        View viewInflate = inflater.inflate(R.layout.fragment_settings, container, false);
        SharedPreferences sharedPreferences = requireActivity().getSharedPreferences("app_preferences", 0);
        Intrinsics.checkNotNullExpressionValue(sharedPreferences, "getSharedPreferences(...)");
        this.sharedPreferences = sharedPreferences;
        Intrinsics.checkNotNull(viewInflate);
        setupBackButton(viewInflate);
        setupThemeSwitch(viewInflate);
        setupLegalLinks(viewInflate);
        setVersionInfo(viewInflate);
        updateTheme(viewInflate);
        return viewInflate;
    }

    private final void setupBackButton(View view) {
        View viewFindViewById = view.findViewById(R.id.backBtn);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        ((MaterialButton) viewFindViewById).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.SettingsFragment$$ExternalSyntheticLambda3
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                SettingsFragment.setupBackButton$lambda$0(this.f$0, view2);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupBackButton$lambda$0(SettingsFragment this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.requireActivity().getSupportFragmentManager().popBackStack();
    }

    private final void setVersionInfo(View view) {
        View viewFindViewById = view.findViewById(R.id.versionInfo);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        ((TextView) viewFindViewById).setText("Version " + requireActivity().getPackageManager().getPackageInfo(requireActivity().getPackageName(), 0).versionName);
    }

    private final void setupThemeSwitch(View view) {
        View viewFindViewById = view.findViewById(R.id.themeOptions);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        RadioGroup radioGroup = (RadioGroup) viewFindViewById;
        SharedPreferences sharedPreferences = this.sharedPreferences;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        String string = sharedPreferences.getString("theme", "auto");
        Log.d("SettingsFragment", "Current selected theme: " + string);
        if (string != null) {
            int iHashCode = string.hashCode();
            if (iHashCode != 3005871) {
                if (iHashCode != 3075958) {
                    if (iHashCode == 102970646 && string.equals("light")) {
                        radioGroup.check(R.id.themeLight);
                    }
                } else if (string.equals("dark")) {
                    radioGroup.check(R.id.themeDark);
                }
            } else if (string.equals("auto")) {
                radioGroup.check(R.id.themeAuto);
            }
        }
        this.isInitialSetup = false;
        radioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() { // from class: com.proxy.videowidget.presentation.fragments.SettingsFragment$$ExternalSyntheticLambda4
            @Override // android.widget.RadioGroup.OnCheckedChangeListener
            public final void onCheckedChanged(RadioGroup radioGroup2, int i) {
                SettingsFragment.setupThemeSwitch$lambda$1(this.f$0, radioGroup2, i);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupThemeSwitch$lambda$1(SettingsFragment this$0, RadioGroup radioGroup, int i) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        if (this$0.isInitialSetup) {
            return;
        }
        SharedPreferences sharedPreferences = this$0.sharedPreferences;
        SharedPreferences sharedPreferences2 = null;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        SharedPreferences.Editor editorEdit = sharedPreferences.edit();
        SharedPreferences sharedPreferences3 = this$0.sharedPreferences;
        if (sharedPreferences3 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
        } else {
            sharedPreferences2 = sharedPreferences3;
        }
        String string = sharedPreferences2.getString("theme", "auto");
        if (i == R.id.themeLight) {
            if (Intrinsics.areEqual(string, "light")) {
                return;
            }
            AppCompatDelegate.setDefaultNightMode(1);
            editorEdit.putString("theme", "light");
            Log.d("SettingsFragment", "Theme changed to light");
        } else if (i == R.id.themeDark) {
            if (Intrinsics.areEqual(string, "dark")) {
                return;
            }
            AppCompatDelegate.setDefaultNightMode(2);
            editorEdit.putString("theme", "dark");
            Log.d("SettingsFragment", "Theme changed to dark");
        } else {
            if (i != R.id.themeAuto || Intrinsics.areEqual(string, "auto")) {
                return;
            }
            AppCompatDelegate.setDefaultNightMode(-1);
            editorEdit.putString("theme", "auto");
            Log.d("SettingsFragment", "Theme changed to auto");
        }
        editorEdit.apply();
        this$0.restartActivity();
    }

    private final void restartActivity() {
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() { // from class: com.proxy.videowidget.presentation.fragments.SettingsFragment$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                SettingsFragment.restartActivity$lambda$2(this.f$0);
            }
        }, 100L);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void restartActivity$lambda$2(SettingsFragment this$0) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        if (this$0.isAdded() && !this$0.isDetached()) {
            this$0.requireActivity().recreate();
        } else {
            Log.d("SettingsFragment", "Fragment not attached, skipping activity recreation");
        }
    }

    private final void updateTheme(View view) throws Resources.NotFoundException {
        int color;
        try {
            boolean z = (getResources().getConfiguration().uiMode & 48) == 32;
            int color2 = getResources().getColor(R.color.blue, null);
            if (z) {
                color = getResources().getColor(R.color.white, null);
            } else {
                color = getResources().getColor(R.color.black, null);
            }
            Integer[] numArr = {Integer.valueOf(R.id.themeLight), Integer.valueOf(R.id.themeDark), Integer.valueOf(R.id.themeAuto)};
            for (int i = 0; i < 3; i++) {
                RadioButton radioButton = (RadioButton) view.findViewById(numArr[i].intValue());
                radioButton.setButtonTintList(new ColorStateList(new int[][]{new int[]{android.R.attr.state_checked}, new int[]{-16842912}}, new int[]{color2, color}));
                radioButton.setTextColor(color);
            }
        } catch (Exception e) {
            Log.e("SettingsFragment", "Error updating theme: " + e.getMessage());
        }
    }

    private final void setupLegalLinks(View view) {
        View viewFindViewById = view.findViewById(R.id.privacyPolicy);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        View viewFindViewById2 = view.findViewById(R.id.tos);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById2, "findViewById(...)");
        ((TextView) viewFindViewById).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.SettingsFragment$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                SettingsFragment.setupLegalLinks$lambda$3(this.f$0, view2);
            }
        });
        ((TextView) viewFindViewById2).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.SettingsFragment$$ExternalSyntheticLambda2
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                SettingsFragment.setupLegalLinks$lambda$4(this.f$0, view2);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupLegalLinks$lambda$3(SettingsFragment this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.openWebPage("https://jolly-pear-e7f.notion.site/Privacy-Policy-d9654a19770244b082055a4ea1c0c23f?pvs=4");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setupLegalLinks$lambda$4(SettingsFragment this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.openWebPage("https://jolly-pear-e7f.notion.site/Terms-of-Service-7ceab033a007436fad0192614d71bac8?pvs=4");
    }

    private final void openWebPage(String url) {
        Uri uri = Uri.parse(url);
        Intrinsics.checkNotNullExpressionValue(uri, "parse(...)");
        Intent intent = new Intent("android.intent.action.VIEW", uri);
        if (intent.resolveActivity(requireActivity().getPackageManager()) != null) {
            startActivity(intent);
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d("SettingsFragment", "onCreate called");
    }

    @Override // androidx.fragment.app.Fragment
    public void onStart() {
        super.onStart();
        Log.d("SettingsFragment", "onStart called");
    }

    @Override // androidx.fragment.app.Fragment
    public void onResume() {
        super.onResume();
        Log.d("SettingsFragment", "onResume called");
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        Log.d("SettingsFragment", "onPause called");
    }

    @Override // androidx.fragment.app.Fragment
    public void onStop() {
        super.onStop();
        Log.d("SettingsFragment", "onStop called");
    }

    @Override // androidx.fragment.app.Fragment
    public void onDestroy() {
        super.onDestroy();
        Log.d("SettingsFragment", "onDestroy called");
    }

    /* compiled from: SettingsFragment.kt */
    @Metadata(d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004¨\u0006\u0005"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/SettingsFragment$Companion;", "", "()V", "newInstance", "Lcom/proxy/videowidget/presentation/fragments/SettingsFragment;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final SettingsFragment newInstance() {
            return new SettingsFragment();
        }
    }
}
