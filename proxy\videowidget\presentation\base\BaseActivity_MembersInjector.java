package com.proxy.videowidget.presentation.base;

import android.app.NotificationManager;
import com.proxy.videowidget.domain.util.pref.MyPref;
import dagger.MembersInjector;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class BaseActivity_MembersInjector implements MembersInjector<BaseActivity> {
    private final Provider<MyPref> myPrefProvider;
    private final Provider<NotificationManager> notificationManagerProvider;

    public BaseActivity_MembersInjector(Provider<MyPref> provider, Provider<NotificationManager> provider2) {
        this.myPrefProvider = provider;
        this.notificationManagerProvider = provider2;
    }

    public static MembersInjector<BaseActivity> create(Provider<MyPref> provider, Provider<NotificationManager> provider2) {
        return new BaseActivity_MembersInjector(provider, provider2);
    }

    @Override // dagger.MembersInjector
    public void injectMembers(BaseActivity baseActivity) {
        injectMyPref(baseActivity, this.myPrefProvider.get());
        injectNotificationManager(baseActivity, this.notificationManagerProvider.get());
    }

    public static void injectMyPref(BaseActivity baseActivity, MyPref myPref) {
        baseActivity.myPref = myPref;
    }

    public static void injectNotificationManager(BaseActivity baseActivity, NotificationManager notificationManager) {
        baseActivity.notificationManager = notificationManager;
    }
}
