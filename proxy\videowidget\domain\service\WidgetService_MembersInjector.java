package com.proxy.videowidget.domain.service;

import android.appwidget.AppWidgetManager;
import dagger.MembersInjector;
import javax.inject.Provider;

/* loaded from: classes3.dex */
public final class WidgetService_MembersInjector implements MembersInjector<WidgetService> {
    private final Provider<AppWidgetManager> appWidgetManagerProvider;

    public WidgetService_MembersInjector(Provider<AppWidgetManager> provider) {
        this.appWidgetManagerProvider = provider;
    }

    public static MembersInjector<WidgetService> create(Provider<AppWidgetManager> provider) {
        return new WidgetService_MembersInjector(provider);
    }

    @Override // dagger.MembersInjector
    public void injectMembers(WidgetService widgetService) {
        injectAppWidgetManager(widgetService, this.appWidgetManagerProvider.get());
    }

    public static void injectAppWidgetManager(WidgetService widgetService, AppWidgetManager appWidgetManager) {
        widgetService.appWidgetManager = appWidgetManager;
    }
}
