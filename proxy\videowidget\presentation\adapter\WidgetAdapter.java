package com.proxy.videowidget.presentation.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.PlayerView;
import com.proxy.videowidget.R;
import com.proxy.videowidget.presentation.adapter.WidgetAdapter;
import com.proxy.videowidget.presentation.widget.Widget;
import java.io.File;
import java.io.FileFilter;
import java.util.Comparator;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.ArraysKt;
import kotlin.collections.CollectionsKt;
import kotlin.comparisons.ComparisonsKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: WidgetAdapter.kt */
@Metadata(d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001 B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0002\u0010\u0006J\b\u0010\u0007\u001a\u00020\bH\u0016J\"\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\nH\u0002J\u001a\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0011\u001a\u00020\u0005H\u0002J\u001c\u0010\u0012\u001a\u00020\u00132\n\u0010\u0014\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0015\u001a\u00020\bH\u0016J\u001c\u0010\u0016\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\bH\u0016J\u0014\u0010\u001a\u001a\u00020\u00132\n\u0010\u0014\u001a\u00060\u0002R\u00020\u0000H\u0016J\u0016\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\bJ\u0014\u0010\u001e\u001a\u00020\u00132\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e¢\u0006\u0002\n\u0000¨\u0006!"}, d2 = {"Lcom/proxy/videowidget/presentation/adapter/WidgetAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/proxy/videowidget/presentation/adapter/WidgetAdapter$WidgetViewHolder;", "widgets", "", "Lcom/proxy/videowidget/presentation/widget/Widget;", "(Ljava/util/List;)V", "getItemCount", "", "getMediaPathForWidget", "", "context", "Landroid/content/Context;", "widgetId", "extension", "loadFirstFrameFromDirectory", "Landroid/graphics/Bitmap;", "widget", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "onViewRecycled", "swapItems", "fromPosition", "toPosition", "updateData", "newWidgetList", "WidgetViewHolder", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WidgetAdapter extends RecyclerView.Adapter<WidgetViewHolder> {
    private List<Widget> widgets;

    public WidgetAdapter(List<Widget> widgets) {
        Intrinsics.checkNotNullParameter(widgets, "widgets");
        this.widgets = widgets;
    }

    /* compiled from: WidgetAdapter.kt */
    @Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004R\u001c\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u0011\u0010\u000b\u001a\u00020\f¢\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u000f\u001a\u00020\u0010¢\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012¨\u0006\u0013"}, d2 = {"Lcom/proxy/videowidget/presentation/adapter/WidgetAdapter$WidgetViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "view", "Landroid/view/View;", "(Lcom/proxy/videowidget/presentation/adapter/WidgetAdapter;Landroid/view/View;)V", "exoPlayer", "Lcom/google/android/exoplayer2/ExoPlayer;", "getExoPlayer", "()Lcom/google/android/exoplayer2/ExoPlayer;", "setExoPlayer", "(Lcom/google/android/exoplayer2/ExoPlayer;)V", "imageView", "Landroid/widget/ImageView;", "getImageView", "()Landroid/widget/ImageView;", "playerView", "Lcom/google/android/exoplayer2/ui/PlayerView;", "getPlayerView", "()Lcom/google/android/exoplayer2/ui/PlayerView;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    public final class WidgetViewHolder extends RecyclerView.ViewHolder {
        private ExoPlayer exoPlayer;
        private final ImageView imageView;
        private final PlayerView playerView;
        final /* synthetic */ WidgetAdapter this$0;

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public WidgetViewHolder(WidgetAdapter widgetAdapter, View view) {
            super(view);
            Intrinsics.checkNotNullParameter(view, "view");
            this.this$0 = widgetAdapter;
            View viewFindViewById = view.findViewById(R.id.imageViewWidget);
            Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
            this.imageView = (ImageView) viewFindViewById;
            View viewFindViewById2 = view.findViewById(R.id.playerView);
            Intrinsics.checkNotNullExpressionValue(viewFindViewById2, "findViewById(...)");
            this.playerView = (PlayerView) viewFindViewById2;
        }

        public final ImageView getImageView() {
            return this.imageView;
        }

        public final PlayerView getPlayerView() {
            return this.playerView;
        }

        public final ExoPlayer getExoPlayer() {
            return this.exoPlayer;
        }

        public final void setExoPlayer(ExoPlayer exoPlayer) {
            this.exoPlayer = exoPlayer;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public WidgetViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        Intrinsics.checkNotNullParameter(parent, "parent");
        View viewInflate = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_widget, parent, false);
        Intrinsics.checkNotNull(viewInflate);
        return new WidgetViewHolder(this, viewInflate);
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x003c  */
    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onBindViewHolder(com.proxy.videowidget.presentation.adapter.WidgetAdapter.WidgetViewHolder r10, int r11) {
        /*
            Method dump skipped, instructions count: 504
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.presentation.adapter.WidgetAdapter.onBindViewHolder(com.proxy.videowidget.presentation.adapter.WidgetAdapter$WidgetViewHolder, int):void");
    }

    /* compiled from: WidgetAdapter.kt */
    @Metadata(d1 = {"\u0000\u001f\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000*\u0001\u0000\b\n\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0016J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0016¨\u0006\t"}, d2 = {"com/proxy/videowidget/presentation/adapter/WidgetAdapter$onBindViewHolder$1", "Lcom/google/android/exoplayer2/Player$Listener;", "onPlaybackStateChanged", "", "state", "", "onPlayerError", "error", "Lcom/google/android/exoplayer2/PlaybackException;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* renamed from: com.proxy.videowidget.presentation.adapter.WidgetAdapter$onBindViewHolder$1, reason: invalid class name */
    public static final class AnonymousClass1 implements Player.Listener {
        final /* synthetic */ Context $context;
        final /* synthetic */ WidgetViewHolder $holder;
        final /* synthetic */ Widget $widget;
        final /* synthetic */ WidgetAdapter this$0;

        AnonymousClass1(WidgetViewHolder widgetViewHolder, WidgetAdapter widgetAdapter, Context context, Widget widget) {
            this.$holder = widgetViewHolder;
            this.this$0 = widgetAdapter;
            this.$context = context;
            this.$widget = widget;
        }

        @Override // com.google.android.exoplayer2.Player.Listener
        public void onPlaybackStateChanged(int state) {
            if (state == 3) {
                View view = this.$holder.itemView;
                final WidgetViewHolder widgetViewHolder = this.$holder;
                view.post(new Runnable() { // from class: com.proxy.videowidget.presentation.adapter.WidgetAdapter$onBindViewHolder$1$$ExternalSyntheticLambda1
                    @Override // java.lang.Runnable
                    public final void run() {
                        WidgetAdapter.AnonymousClass1.onPlaybackStateChanged$lambda$0(widgetViewHolder);
                    }
                });
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static final void onPlaybackStateChanged$lambda$0(WidgetViewHolder holder) {
            Intrinsics.checkNotNullParameter(holder, "$holder");
            holder.getPlayerView().setVisibility(0);
            holder.getImageView().setVisibility(8);
        }

        @Override // com.google.android.exoplayer2.Player.Listener
        public void onPlayerError(PlaybackException error) {
            Intrinsics.checkNotNullParameter(error, "error");
            View view = this.$holder.itemView;
            final WidgetViewHolder widgetViewHolder = this.$holder;
            final WidgetAdapter widgetAdapter = this.this$0;
            final Context context = this.$context;
            final Widget widget = this.$widget;
            view.post(new Runnable() { // from class: com.proxy.videowidget.presentation.adapter.WidgetAdapter$onBindViewHolder$1$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() {
                    WidgetAdapter.AnonymousClass1.onPlayerError$lambda$1(widgetViewHolder, widgetAdapter, context, widget);
                }
            });
            Log.e("WidgetAdapter", "ExoPlayer Error: " + error.getMessage());
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static final void onPlayerError$lambda$1(WidgetViewHolder holder, WidgetAdapter this$0, Context context, Widget widget) {
            Intrinsics.checkNotNullParameter(holder, "$holder");
            Intrinsics.checkNotNullParameter(this$0, "this$0");
            Intrinsics.checkNotNullParameter(widget, "$widget");
            holder.getPlayerView().setVisibility(8);
            holder.getImageView().setVisibility(0);
            Intrinsics.checkNotNull(context);
            Bitmap bitmapLoadFirstFrameFromDirectory = this$0.loadFirstFrameFromDirectory(context, widget);
            if (bitmapLoadFirstFrameFromDirectory != null) {
                holder.getImageView().setImageBitmap(bitmapLoadFirstFrameFromDirectory);
            } else {
                holder.getImageView().setImageResource(R.drawable.placeholder_for_future);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public int getItemCount() {
        return this.widgets.size();
    }

    public final void swapItems(int fromPosition, int toPosition) {
        Widget widget = this.widgets.get(fromPosition);
        this.widgets.remove(fromPosition);
        this.widgets.add(toPosition > fromPosition ? toPosition - 1 : toPosition, widget);
        notifyItemMoved(fromPosition, toPosition);
    }

    public final void updateData(List<Widget> newWidgetList) {
        Intrinsics.checkNotNullParameter(newWidgetList, "newWidgetList");
        this.widgets = newWidgetList;
        notifyDataSetChanged();
    }

    private final String getMediaPathForWidget(Context context, int widgetId, String extension) {
        File file = new File(context.getFilesDir(), "media_" + widgetId + '.' + extension);
        if (file.exists()) {
            return file.getAbsolutePath();
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final Bitmap loadFirstFrameFromDirectory(Context context, Widget widget) {
        List listSortedWith;
        String framesDirectory = widget.getFramesDirectory();
        if (framesDirectory != null) {
            File file = new File(framesDirectory);
            if (file.exists() && file.isDirectory()) {
                File[] fileArrListFiles = file.listFiles(new FileFilter() { // from class: com.proxy.videowidget.presentation.adapter.WidgetAdapter$$ExternalSyntheticLambda0
                    @Override // java.io.FileFilter
                    public final boolean accept(File file2) {
                        return WidgetAdapter.loadFirstFrameFromDirectory$lambda$2(file2);
                    }
                });
                File file2 = (fileArrListFiles == null || (listSortedWith = ArraysKt.sortedWith(fileArrListFiles, new Comparator() { // from class: com.proxy.videowidget.presentation.adapter.WidgetAdapter$loadFirstFrameFromDirectory$$inlined$sortedBy$1
                    /* JADX WARN: Multi-variable type inference failed */
                    @Override // java.util.Comparator
                    public final int compare(T t, T t2) {
                        return ComparisonsKt.compareValues(((File) t).getName(), ((File) t2).getName());
                    }
                })) == null) ? null : (File) CollectionsKt.firstOrNull(listSortedWith);
                if (file2 != null && file2.exists()) {
                    return BitmapFactory.decodeFile(file2.getAbsolutePath());
                }
            }
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final boolean loadFirstFrameFromDirectory$lambda$2(File file) {
        if (!file.isFile()) {
            return false;
        }
        String name = file.getName();
        Intrinsics.checkNotNullExpressionValue(name, "getName(...)");
        if (!StringsKt.startsWith$default(name, "frame_", false, 2, (Object) null)) {
            return false;
        }
        String name2 = file.getName();
        Intrinsics.checkNotNullExpressionValue(name2, "getName(...)");
        if (!StringsKt.endsWith$default(name2, ".png", false, 2, (Object) null)) {
            String name3 = file.getName();
            Intrinsics.checkNotNullExpressionValue(name3, "getName(...)");
            if (!StringsKt.endsWith$default(name3, ".jpg", false, 2, (Object) null)) {
                String name4 = file.getName();
                Intrinsics.checkNotNullExpressionValue(name4, "getName(...)");
                if (!StringsKt.endsWith$default(name4, ".jpeg", false, 2, (Object) null)) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public void onViewRecycled(WidgetViewHolder holder) {
        Intrinsics.checkNotNullParameter(holder, "holder");
        ExoPlayer exoPlayer = holder.getExoPlayer();
        if (exoPlayer != null) {
            exoPlayer.release();
        }
        holder.setExoPlayer(null);
        holder.getPlayerView().setPlayer(null);
        super.onViewRecycled((WidgetAdapter) holder);
    }
}
