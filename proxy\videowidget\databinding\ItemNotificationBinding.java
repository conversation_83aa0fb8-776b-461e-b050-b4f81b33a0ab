package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class ItemNotificationBinding implements ViewBinding {
    public final MaterialButton btnNotificationAction;
    private final CardView rootView;
    public final TextView tvNotificationDescription;
    public final TextView tvNotificationTitle;

    private ItemNotificationBinding(CardView cardView, MaterialButton materialButton, TextView textView, TextView textView2) {
        this.rootView = cardView;
        this.btnNotificationAction = materialButton;
        this.tvNotificationDescription = textView;
        this.tvNotificationTitle = textView2;
    }

    @Override // androidx.viewbinding.ViewBinding
    public CardView getRoot() {
        return this.rootView;
    }

    public static ItemNotificationBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static ItemNotificationBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.item_notification, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static ItemNotificationBinding bind(View view) {
        int i = R.id.btnNotificationAction;
        MaterialButton materialButton = (MaterialButton) ViewBindings.findChildViewById(view, i);
        if (materialButton != null) {
            i = R.id.tvNotificationDescription;
            TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
            if (textView != null) {
                i = R.id.tvNotificationTitle;
                TextView textView2 = (TextView) ViewBindings.findChildViewById(view, i);
                if (textView2 != null) {
                    return new ItemNotificationBinding((CardView) view, materialButton, textView, textView2);
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
