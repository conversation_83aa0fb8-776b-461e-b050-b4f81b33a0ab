package com.proxy.videowidget.presentation.base;

import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.proxy.videowidget.R;
import com.proxy.videowidget.domain.util.ConstantsKt;
import com.proxy.videowidget.domain.util.permission.PermissionsListener;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: BasePermissionsActivity.kt */
@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\b&\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0006J\u0018\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\tH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0012"}, d2 = {"Lcom/proxy/videowidget/presentation/base/BasePermissionsActivity;", "Lcom/proxy/videowidget/presentation/base/BaseActivity;", "()V", "isDialogShowing", "", "permissionsListener", "Lcom/proxy/videowidget/domain/util/permission/PermissionsListener;", "storagePermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "storageSettingLauncher", "Landroid/content/Intent;", "getStoragePermission", "", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "showPermissionsSettingDialog", "title", NotificationCompat.CATEGORY_MESSAGE, "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public abstract class BasePermissionsActivity extends BaseActivity {
    private boolean isDialogShowing;
    private PermissionsListener permissionsListener;
    private ActivityResultLauncher<String> storagePermissionLauncher = registerForActivityResult(new ActivityResultContracts.RequestPermission(), new ActivityResultCallback() { // from class: com.proxy.videowidget.presentation.base.BasePermissionsActivity$$ExternalSyntheticLambda1
        @Override // androidx.activity.result.ActivityResultCallback
        public final void onActivityResult(Object obj) {
            BasePermissionsActivity.storagePermissionLauncher$lambda$0(this.f$0, ((Boolean) obj).booleanValue());
        }
    });
    private final ActivityResultLauncher<Intent> storageSettingLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback() { // from class: com.proxy.videowidget.presentation.base.BasePermissionsActivity$$ExternalSyntheticLambda2
        @Override // androidx.activity.result.ActivityResultCallback
        public final void onActivityResult(Object obj) throws Resources.NotFoundException {
            BasePermissionsActivity.storageSettingLauncher$lambda$1(this.f$0, (ActivityResult) obj);
        }
    });

    /* JADX INFO: Access modifiers changed from: private */
    public static final void storagePermissionLauncher$lambda$0(BasePermissionsActivity this$0, boolean z) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        try {
            if (z) {
                PermissionsListener permissionsListener = this$0.permissionsListener;
                if (permissionsListener != null) {
                    permissionsListener.onPermissionGranted();
                }
            } else {
                String string = this$0.getMContext().getString(R.string.video_library_access);
                Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                String string2 = this$0.getMContext().getString(R.string.video_message);
                Intrinsics.checkNotNullExpressionValue(string2, "getString(...)");
                this$0.showPermissionsSettingDialog(string, string2);
            }
        } catch (Exception unused) {
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void storageSettingLauncher$lambda$1(BasePermissionsActivity this$0, ActivityResult it) throws Resources.NotFoundException {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(it, "it");
        try {
            if (!ConstantsKt.checkStoragePermission(this$0.getMContext())) {
                String string = this$0.getResources().getString(R.string.video_library_access);
                Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                String string2 = this$0.getResources().getString(R.string.video_message);
                Intrinsics.checkNotNullExpressionValue(string2, "getString(...)");
                this$0.showPermissionsSettingDialog(string, string2);
            } else {
                PermissionsListener permissionsListener = this$0.permissionsListener;
                if (permissionsListener != null) {
                    permissionsListener.onPermissionGranted();
                }
            }
        } catch (Exception unused) {
        }
    }

    public final void getStoragePermission(PermissionsListener listener) {
        Intrinsics.checkNotNullParameter(listener, "listener");
        this.permissionsListener = listener;
        if (!ConstantsKt.checkStoragePermission(getMContext())) {
            if (ConstantsKt.isAndroid13()) {
                this.storagePermissionLauncher.launch("android.permission.READ_MEDIA_VIDEO");
                return;
            } else {
                this.storagePermissionLauncher.launch("android.permission.READ_EXTERNAL_STORAGE");
                return;
            }
        }
        PermissionsListener permissionsListener = this.permissionsListener;
        if (permissionsListener != null) {
            permissionsListener.onPermissionGranted();
        }
    }

    private final void showPermissionsSettingDialog(String title, String msg) {
        TextView textView;
        if (this.isDialogShowing) {
            return;
        }
        AlertDialog alertDialogCreate = new MaterialAlertDialogBuilder(getMContext()).setTitle((CharSequence) title).setMessage((CharSequence) msg).setCancelable(false).setPositiveButton((CharSequence) getResources().getString(R.string.go_to_settings), new DialogInterface.OnClickListener() { // from class: com.proxy.videowidget.presentation.base.BasePermissionsActivity$$ExternalSyntheticLambda0
            @Override // android.content.DialogInterface.OnClickListener
            public final void onClick(DialogInterface dialogInterface, int i) {
                BasePermissionsActivity.showPermissionsSettingDialog$lambda$4(this.f$0, dialogInterface, i);
            }
        }).create();
        Intrinsics.checkNotNullExpressionValue(alertDialogCreate, "create(...)");
        if (alertDialogCreate.isShowing()) {
            return;
        }
        alertDialogCreate.show();
        this.isDialogShowing = true;
        Button button = alertDialogCreate.getButton(-1);
        Button button2 = alertDialogCreate.getButton(-2);
        Window window = alertDialogCreate.getWindow();
        if (window != null && (textView = (TextView) window.findViewById(android.R.id.message)) != null) {
            textView.setTextColor(ContextCompat.getColor(getMContext(), R.color.black));
        }
        button.setAllCaps(false);
        button2.setAllCaps(false);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showPermissionsSettingDialog$lambda$4(BasePermissionsActivity this$0, DialogInterface dialogInterface, int i) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        dialogInterface.dismiss();
        this$0.isDialogShowing = false;
        Intent intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS");
        intent.setData(Uri.fromParts("package", this$0.getPackageName(), null));
        try {
            this$0.storageSettingLauncher.launch(intent);
        } catch (Exception unused) {
        }
    }
}
