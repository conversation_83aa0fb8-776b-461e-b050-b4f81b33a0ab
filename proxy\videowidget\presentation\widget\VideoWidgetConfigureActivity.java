package com.proxy.videowidget.presentation.widget;

import android.appwidget.AppWidgetManager;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowInsetsController;
import android.widget.Toast;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.PickVisualMediaRequest;
import androidx.activity.result.PickVisualMediaRequestKt;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.request.BaseRequestOptions;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;
import com.proxy.videowidget.R;
import com.proxy.videowidget.databinding.VideoWidgetConfigureBinding;
import com.proxy.videowidget.domain.service.WidgetService;
import com.proxy.videowidget.presentation.widget.VideoWidget;
import com.proxy.videowidget.repository.WidgetRepository;
import dagger.hilt.android.AndroidEntryPoint;
import java.io.File;
import java.io.FileFilter;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.io.ByteStreamsKt;
import kotlin.io.CloseableKt;
import kotlin.io.FilesKt;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: VideoWidgetConfigureActivity.kt */
@Metadata(d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u0007\u0018\u0000 32\u00020\u0001:\u00013B\u0005¢\u0006\u0002\u0010\u0002J\b\u0010\u001a\u001a\u00020\u001bH\u0002J\b\u0010\u001c\u001a\u00020\u001bH\u0002J\b\u0010\u001d\u001a\u00020\u001bH\u0002J\u0010\u0010\u001e\u001a\u00020\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u001a\u0010\u001f\u001a\u0004\u0018\u00010\n2\u0006\u0010 \u001a\u00020\u00192\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\"\u0010!\u001a\u0004\u0018\u00010\n2\u0006\u0010 \u001a\u00020\u00192\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0002J\u0010\u0010\"\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u0019H\u0002J\u0010\u0010#\u001a\u00020\u001b2\u0006\u0010 \u001a\u00020\u0019H\u0002J\u0012\u0010$\u001a\u00020\u001b2\b\u0010%\u001a\u0004\u0018\u00010&H\u0014J\b\u0010'\u001a\u00020\u001bH\u0002J\b\u0010(\u001a\u00020\u001bH\u0002J\b\u0010)\u001a\u00020\u001bH\u0002J\b\u0010*\u001a\u00020\u001bH\u0002J\b\u0010+\u001a\u00020\u001bH\u0002J\u0010\u0010,\u001a\u00020\u001b2\u0006\u0010-\u001a\u00020\nH\u0002J\b\u0010.\u001a\u00020\u001bH\u0002J\u0010\u0010/\u001a\u00020\u001b2\u0006\u00100\u001a\u000201H\u0002J\u0010\u00102\u001a\u00020\u001b2\u0006\u0010 \u001a\u00020\u0019H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u000fX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.¢\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\nX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0082\u000e¢\u0006\u0002\n\u0000¨\u00064"}, d2 = {"Lcom/proxy/videowidget/presentation/widget/VideoWidgetConfigureActivity;", "Lcom/proxy/videowidget/presentation/base/BasePermissionsActivity;", "()V", "MAX_DURATION_MS", "", "appWidgetId", "", "binding", "Lcom/proxy/videowidget/databinding/VideoWidgetConfigureBinding;", "mediaType", "", "onAddButtonClickListener", "Landroid/view/View$OnClickListener;", "onMediaSelectClickListener", "pickMediaLegacy", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "pickVisualMedia", "Landroidx/activity/result/PickVisualMediaRequest;", "sharedPreferences", "Landroid/content/SharedPreferences;", "toast", "Landroid/widget/Toast;", "videoInputPath", "videoInputUri", "Landroid/net/Uri;", "addWidget", "", "adjustSystemBarsAppearance", "checkAndShowFeedbackDialog", "cleanupPreviousWidgetData", "copyImageToInternalStorage", "uri", "copyUriToInternalStorage", "getVideoDuration", "handleMediaSelection", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "processGifWidget", "processImageWidget", "processVideoWidget", "setupClickListeners", "showFeedbackDialog", "showToast", "message", "startWidgets", "updateAddButtonState", "isEnabled", "", "updateMediaSelection", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@AndroidEntryPoint
/* loaded from: classes3.dex */
public final class VideoWidgetConfigureActivity extends Hilt_VideoWidgetConfigureActivity {
    public static final String KEY_LAST_FEEDBACK_SHOWN = "lastFeedbackShown";
    public static final String PREFS_NAME = "VideoWidgetPrefs";
    private int appWidgetId;
    private VideoWidgetConfigureBinding binding;
    private SharedPreferences sharedPreferences;
    private Toast toast;
    private Uri videoInputUri;
    private String videoInputPath = "";
    private String mediaType = "video";
    private final long MAX_DURATION_MS = 30000;
    private final View.OnClickListener onAddButtonClickListener = new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda3
        @Override // android.view.View.OnClickListener
        public final void onClick(View view) throws IOException {
            VideoWidgetConfigureActivity.onAddButtonClickListener$lambda$0(this.f$0, view);
        }
    };
    private final View.OnClickListener onMediaSelectClickListener = new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda4
        @Override // android.view.View.OnClickListener
        public final void onClick(View view) {
            VideoWidgetConfigureActivity.onMediaSelectClickListener$lambda$7(this.f$0, view);
        }
    };
    private final ActivityResultLauncher<PickVisualMediaRequest> pickVisualMedia = registerForActivityResult(new ActivityResultContracts.PickVisualMedia(), new ActivityResultCallback() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda5
        @Override // androidx.activity.result.ActivityResultCallback
        public final void onActivityResult(Object obj) {
            VideoWidgetConfigureActivity.pickVisualMedia$lambda$9(this.f$0, (Uri) obj);
        }
    });
    private final ActivityResultLauncher<Intent> pickMediaLegacy = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda6
        @Override // androidx.activity.result.ActivityResultCallback
        public final void onActivityResult(Object obj) {
            VideoWidgetConfigureActivity.pickMediaLegacy$lambda$11(this.f$0, (ActivityResult) obj);
        }
    });

    @Override // com.proxy.videowidget.presentation.base.BaseActivity, com.proxy.videowidget.presentation.base.Hilt_BaseActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        String string = getSharedPreferences("app_preferences", 0).getString("theme", "auto");
        if (string != null) {
            int iHashCode = string.hashCode();
            if (iHashCode != 3005871) {
                if (iHashCode != 3075958) {
                    if (iHashCode == 102970646 && string.equals("light")) {
                        AppCompatDelegate.setDefaultNightMode(1);
                        setTheme(R.style.Theme_VideoWidget_Light);
                    }
                } else if (string.equals("dark")) {
                    AppCompatDelegate.setDefaultNightMode(2);
                    setTheme(R.style.Theme_VideoWidget_Dark);
                }
            } else if (string.equals("auto")) {
                AppCompatDelegate.setDefaultNightMode(-1);
            }
        }
        super.onCreate(savedInstanceState);
        VideoWidgetConfigureActivity videoWidgetConfigureActivity = this;
        getWindow().setNavigationBarColor(ContextCompat.getColor(videoWidgetConfigureActivity, R.color.background));
        getWindow().setStatusBarColor(ContextCompat.getColor(videoWidgetConfigureActivity, R.color.background));
        adjustSystemBarsAppearance();
        setResult(0);
        VideoWidgetConfigureBinding videoWidgetConfigureBindingInflate = VideoWidgetConfigureBinding.inflate(getLayoutInflater());
        Intrinsics.checkNotNullExpressionValue(videoWidgetConfigureBindingInflate, "inflate(...)");
        this.binding = videoWidgetConfigureBindingInflate;
        if (videoWidgetConfigureBindingInflate == null) {
            Intrinsics.throwUninitializedPropertyAccessException("binding");
            videoWidgetConfigureBindingInflate = null;
        }
        setContentView(videoWidgetConfigureBindingInflate.getRoot());
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            this.appWidgetId = extras.getInt("appWidgetId", 0);
            Log.d("VideoWidgetConfigure", "Retrieved AppWidgetId: " + this.appWidgetId);
        }
        if (this.appWidgetId == 0) {
            Log.e("VideoWidgetConfigure", "Invalid AppWidgetId received.");
            finishAndRemoveTask();
            return;
        }
        SharedPreferences sharedPreferences = getSharedPreferences("VideoWidgetPrefs", 0);
        Intrinsics.checkNotNullExpressionValue(sharedPreferences, "getSharedPreferences(...)");
        this.sharedPreferences = sharedPreferences;
        cleanupPreviousWidgetData(this.appWidgetId);
        startWidgets();
        setupClickListeners();
        updateAddButtonState(false);
        checkAndShowFeedbackDialog();
    }

    private final void adjustSystemBarsAppearance() {
        if (Build.VERSION.SDK_INT >= 30) {
            WindowInsetsController insetsController = getWindow().getInsetsController();
            if (insetsController != null) {
                if ((getResources().getConfiguration().uiMode & 48) == 16) {
                    insetsController.setSystemBarsAppearance(24, 24);
                    return;
                } else {
                    insetsController.setSystemBarsAppearance(0, 24);
                    return;
                }
            }
            return;
        }
        if (Build.VERSION.SDK_INT >= 26) {
            if ((getResources().getConfiguration().uiMode & 48) == 16) {
                getWindow().getDecorView().setSystemUiVisibility(8208);
                return;
            } else {
                getWindow().getDecorView().setSystemUiVisibility(0);
                return;
            }
        }
        getWindow().getDecorView().setSystemUiVisibility(0);
    }

    private final void startWidgets() {
        WidgetRepository.INSTANCE.getWidgets().observe(this, new VideoWidgetConfigureActivity$sam$androidx_lifecycle_Observer$0(new Function1<List<? extends Widget>, Unit>() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity.startWidgets.1
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Unit invoke(List<? extends Widget> list) {
                invoke2((List<Widget>) list);
                return Unit.INSTANCE;
            }

            /* renamed from: invoke, reason: avoid collision after fix types in other method */
            public final void invoke2(List<Widget> list) {
                Intrinsics.checkNotNull(list);
                VideoWidgetConfigureActivity videoWidgetConfigureActivity = VideoWidgetConfigureActivity.this;
                for (Widget widget : list) {
                    if (!WidgetService.INSTANCE.isWidgetActive(widget.getId())) {
                        Intent intent = new Intent(videoWidgetConfigureActivity, (Class<?>) WidgetService.class);
                        intent.putExtra("appWidgetId", widget.getId());
                        intent.putExtra("framesDirectory", widget.getFramesDirectory());
                        String mediaType = widget.getMediaType();
                        if (mediaType == null) {
                            mediaType = "video";
                        }
                        intent.putExtra("mediaType", mediaType);
                        try {
                            videoWidgetConfigureActivity.startService(intent);
                            Log.d("VideoWidgetConfigure", "Started WidgetService for widget ID: " + widget.getId());
                        } catch (Exception e) {
                            e.printStackTrace();
                            Log.e("VideoWidgetConfigure", "Failed to start WidgetService for widget ID: " + widget.getId());
                        }
                    }
                }
            }
        }));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void onAddButtonClickListener$lambda$0(VideoWidgetConfigureActivity this$0, View view) throws IOException {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.addWidget();
    }

    private final void addWidget() throws IOException {
        Log.d("VideoWidgetConfigure", "Add Widget button clicked.");
        if (this.videoInputUri == null) {
            Log.e("VideoWidgetConfigure", "No media Uri found.");
            showToast("Upload a video");
            return;
        }
        String str = this.mediaType;
        int iHashCode = str.hashCode();
        if (iHashCode != 102340) {
            if (iHashCode != 100313435) {
                if (iHashCode == 112202875 && str.equals("video")) {
                    Uri uri = this.videoInputUri;
                    Intrinsics.checkNotNull(uri);
                    String strCopyUriToInternalStorage = copyUriToInternalStorage(uri, this.appWidgetId, "video");
                    String str2 = strCopyUriToInternalStorage;
                    if (str2 == null || str2.length() == 0) {
                        Log.e("VideoWidgetConfigure", "Failed to copy media to internal storage.");
                        showToast("Failed to copy media. Please try again.");
                        return;
                    } else {
                        this.videoInputPath = strCopyUriToInternalStorage;
                        processVideoWidget();
                        return;
                    }
                }
            } else if (str.equals("image")) {
                Uri uri2 = this.videoInputUri;
                Intrinsics.checkNotNull(uri2);
                String strCopyImageToInternalStorage = copyImageToInternalStorage(uri2, this.appWidgetId);
                String str3 = strCopyImageToInternalStorage;
                if (str3 == null || str3.length() == 0) {
                    Log.e("VideoWidgetConfigure", "Failed to copy image to internal storage.");
                    showToast("Failed to copy image. Please try again.");
                    return;
                } else {
                    this.videoInputPath = strCopyImageToInternalStorage;
                    processImageWidget();
                    return;
                }
            }
        } else if (str.equals("gif")) {
            Uri uri3 = this.videoInputUri;
            Intrinsics.checkNotNull(uri3);
            String strCopyUriToInternalStorage2 = copyUriToInternalStorage(uri3, this.appWidgetId, "gif");
            String str4 = strCopyUriToInternalStorage2;
            if (str4 == null || str4.length() == 0) {
                Log.e("VideoWidgetConfigure", "Failed to copy media to internal storage.");
                showToast("Failed to copy media. Please try again.");
                return;
            } else {
                this.videoInputPath = strCopyUriToInternalStorage2;
                processGifWidget();
                return;
            }
        }
        showToast("Unsupported media type.");
    }

    private final void processVideoWidget() throws IOException {
        Uri uri = this.videoInputUri;
        Intrinsics.checkNotNull(uri);
        long videoDuration = getVideoDuration(uri);
        Log.d("VideoWidgetConfigure", "Video duration: " + videoDuration + " ms");
        if (videoDuration <= this.MAX_DURATION_MS) {
            Log.d("VideoWidgetConfigure", "Video duration is within the allowed limit.");
            Intent intent = new Intent();
            intent.putExtra("appWidgetId", this.appWidgetId);
            setResult(-1, intent);
            Intent intent2 = new Intent(this, (Class<?>) WidgetService.class);
            intent2.putExtra("appWidgetId", this.appWidgetId);
            intent2.putExtra("videoInputPath", this.videoInputPath);
            intent2.putExtra("mediaType", "video");
            try {
                startService(intent2);
                Log.d("VideoWidgetConfigure", "Started WidgetService with video path: " + this.videoInputPath);
                finishAndRemoveTask();
                return;
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("VideoWidgetConfigure", "Failed to start WidgetService.", e);
                showToast("Failed to start widget service.");
                return;
            }
        }
        Log.e("VideoWidgetConfigure", "Video duration exceeds the allowed limit.");
        showToast("Video should be less than 30 seconds");
    }

    private final void processGifWidget() {
        Intent intent = new Intent();
        intent.putExtra("appWidgetId", this.appWidgetId);
        setResult(-1, intent);
        Intent intent2 = new Intent(this, (Class<?>) WidgetService.class);
        intent2.putExtra("appWidgetId", this.appWidgetId);
        intent2.putExtra("gifInputPath", this.videoInputPath);
        intent2.putExtra("mediaType", "gif");
        try {
            startService(intent2);
            Log.d("VideoWidgetConfigure", "Started WidgetService with GIF path: " + this.videoInputPath);
            finishAndRemoveTask();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("VideoWidgetConfigure", "Failed to start WidgetService.", e);
            showToast("Failed to start widget service.");
        }
    }

    private final void processImageWidget() {
        Bitmap bitmapDecodeFile = BitmapFactory.decodeFile(this.videoInputPath);
        if (bitmapDecodeFile == null) {
            showToast("Failed to decode image.");
            return;
        }
        WidgetRepository.INSTANCE.addOrUpdateWidget(new Widget(this.appWidgetId, "Image Widget " + this.appWidgetId, "Image widget description", bitmapDecodeFile, null, 0.0f, "image"));
        WidgetRepository.INSTANCE.saveBitmapToFile(this.appWidgetId, bitmapDecodeFile, true);
        Intent intent = new Intent();
        intent.putExtra("appWidgetId", this.appWidgetId);
        setResult(-1, intent);
        VideoWidgetConfigureActivity videoWidgetConfigureActivity = this;
        AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(videoWidgetConfigureActivity);
        VideoWidget.Companion companion = VideoWidget.INSTANCE;
        Intrinsics.checkNotNull(appWidgetManager);
        companion.updateAppWidget(videoWidgetConfigureActivity, appWidgetManager, this.appWidgetId, bitmapDecodeFile);
        finishAndRemoveTask();
    }

    private final long getVideoDuration(Uri uri) throws IOException {
        Long longOrNull;
        MediaMetadataRetriever mediaMetadataRetriever = new MediaMetadataRetriever();
        long jLongValue = 0;
        try {
            try {
                mediaMetadataRetriever.setDataSource(this, uri);
                String strExtractMetadata = mediaMetadataRetriever.extractMetadata(9);
                if (strExtractMetadata != null && (longOrNull = StringsKt.toLongOrNull(strExtractMetadata)) != null) {
                    jLongValue = longOrNull.longValue();
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("VideoWidgetConfigure", "Failed to retrieve video duration.");
            }
            return jLongValue;
        } finally {
            mediaMetadataRetriever.release();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void onMediaSelectClickListener$lambda$7(VideoWidgetConfigureActivity this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        if (Build.VERSION.SDK_INT >= 33) {
            Log.d("VideoWidgetConfigure", "Launching Photo Picker (Android 13+).");
            this$0.pickVisualMedia.launch(PickVisualMediaRequestKt.PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.VideoOnly.INSTANCE));
            return;
        }
        Intent intent = new Intent("android.intent.action.OPEN_DOCUMENT");
        intent.addCategory("android.intent.category.OPENABLE");
        intent.setType("*/*");
        intent.putExtra("android.intent.extra.MIME_TYPES", new String[]{"video/*"});
        this$0.pickMediaLegacy.launch(intent);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void pickVisualMedia$lambda$9(VideoWidgetConfigureActivity this$0, Uri uri) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        if (uri != null) {
            this$0.handleMediaSelection(uri);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void pickMediaLegacy$lambda$11(VideoWidgetConfigureActivity this$0, ActivityResult result) {
        Intent data;
        Uri data2;
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(result, "result");
        if (result.getResultCode() != -1 || (data = result.getData()) == null || (data2 = data.getData()) == null) {
            return;
        }
        this$0.handleMediaSelection(data2);
    }

    private final void handleMediaSelection(Uri uri) {
        String type = getContentResolver().getType(uri);
        if (type != null) {
            if (StringsKt.startsWith$default(type, "video/", false, 2, (Object) null)) {
                this.videoInputUri = uri;
                this.mediaType = "video";
                updateMediaSelection(uri);
                updateAddButtonState(true);
                return;
            }
            showToast("Unsupported file type.");
            return;
        }
        showToast("Failed to get file type.");
    }

    private final void updateMediaSelection(Uri uri) {
        RequestBuilder<Drawable> requestBuilderApply = Glide.with((FragmentActivity) this).load(uri).apply((BaseRequestOptions<?>) RequestOptions.centerCropTransform().placeholder(R.drawable.placeholder));
        VideoWidgetConfigureBinding videoWidgetConfigureBinding = this.binding;
        if (videoWidgetConfigureBinding == null) {
            Intrinsics.throwUninitializedPropertyAccessException("binding");
            videoWidgetConfigureBinding = null;
        }
        requestBuilderApply.into(videoWidgetConfigureBinding.thumbnail);
    }

    private final void cleanupPreviousWidgetData(final int appWidgetId) {
        File file = new File(getApplicationContext().getFilesDir(), "frames_" + appWidgetId);
        if (file.exists()) {
            try {
                FilesKt.deleteRecursively(file);
                Log.d("VideoWidgetConfigure", "Deleted frames directory: " + file.getAbsolutePath());
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("VideoWidgetConfigure", "Failed to delete frames directory: " + file.getAbsolutePath());
            }
        }
        File[] fileArrListFiles = getApplicationContext().getFilesDir().listFiles(new FileFilter() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda1
            @Override // java.io.FileFilter
            public final boolean accept(File file2) {
                return VideoWidgetConfigureActivity.cleanupPreviousWidgetData$lambda$12(appWidgetId, file2);
            }
        });
        if (fileArrListFiles != null) {
            for (File file2 : fileArrListFiles) {
                if (file2.exists()) {
                    try {
                        file2.delete();
                        Log.d("VideoWidgetConfigure", "Deleted media file: " + file2.getAbsolutePath());
                    } catch (Exception e2) {
                        e2.printStackTrace();
                        Log.e("VideoWidgetConfigure", "Failed to delete media file: " + file2.getAbsolutePath());
                    }
                }
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final boolean cleanupPreviousWidgetData$lambda$12(int i, File file) {
        String name = file.getName();
        Intrinsics.checkNotNullExpressionValue(name, "getName(...)");
        if (!StringsKt.startsWith$default(name, "media_" + i, false, 2, (Object) null)) {
            return false;
        }
        String name2 = file.getName();
        Intrinsics.checkNotNullExpressionValue(name2, "getName(...)");
        if (!StringsKt.endsWith$default(name2, ".mp4", false, 2, (Object) null)) {
            String name3 = file.getName();
            Intrinsics.checkNotNullExpressionValue(name3, "getName(...)");
            if (!StringsKt.endsWith$default(name3, ".avi", false, 2, (Object) null)) {
                String name4 = file.getName();
                Intrinsics.checkNotNullExpressionValue(name4, "getName(...)");
                if (!StringsKt.endsWith$default(name4, ".mkv", false, 2, (Object) null)) {
                    String name5 = file.getName();
                    Intrinsics.checkNotNullExpressionValue(name5, "getName(...)");
                    if (!StringsKt.endsWith$default(name5, ".gif", false, 2, (Object) null)) {
                        String name6 = file.getName();
                        Intrinsics.checkNotNullExpressionValue(name6, "getName(...)");
                        if (!StringsKt.endsWith$default(name6, ".jpg", false, 2, (Object) null)) {
                            String name7 = file.getName();
                            Intrinsics.checkNotNullExpressionValue(name7, "getName(...)");
                            if (!StringsKt.endsWith$default(name7, ".png", false, 2, (Object) null)) {
                                return false;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    private final void setupClickListeners() {
        VideoWidgetConfigureBinding videoWidgetConfigureBinding = this.binding;
        if (videoWidgetConfigureBinding == null) {
            Intrinsics.throwUninitializedPropertyAccessException("binding");
            videoWidgetConfigureBinding = null;
        }
        videoWidgetConfigureBinding.addButton.setOnClickListener(this.onAddButtonClickListener);
        videoWidgetConfigureBinding.uploadVideo.setOnClickListener(this.onMediaSelectClickListener);
    }

    private final void showToast(String message) {
        Toast toast = this.toast;
        if (toast != null) {
            toast.cancel();
        }
        Toast toastMakeText = Toast.makeText(this, message, 0);
        this.toast = toastMakeText;
        if (toastMakeText != null) {
            toastMakeText.show();
        }
    }

    private final void updateAddButtonState(boolean isEnabled) {
        VideoWidgetConfigureBinding videoWidgetConfigureBinding = this.binding;
        if (videoWidgetConfigureBinding == null) {
            Intrinsics.throwUninitializedPropertyAccessException("binding");
            videoWidgetConfigureBinding = null;
        }
        AppCompatButton appCompatButton = videoWidgetConfigureBinding.addButton;
        appCompatButton.setEnabled(isEnabled);
        appCompatButton.setClickable(isEnabled);
        if (isEnabled) {
            appCompatButton.setBackgroundResource(R.drawable.btn_background);
            appCompatButton.setTextColor(ContextCompat.getColor(this, R.color.white));
        } else {
            appCompatButton.setBackgroundResource(R.drawable.btn_disabled_background);
            appCompatButton.setTextColor(ContextCompat.getColor(this, R.color.text_disabled_color));
        }
    }

    private final String copyUriToInternalStorage(Uri uri, int appWidgetId, String mediaType) throws FileNotFoundException {
        String str = "gif";
        try {
            InputStream inputStreamOpenInputStream = getContentResolver().openInputStream(uri);
            if (inputStreamOpenInputStream == null) {
                return null;
            }
            if (Intrinsics.areEqual(mediaType, "video")) {
                str = "mp4";
            } else if (!Intrinsics.areEqual(mediaType, "gif")) {
                str = "dat";
            }
            String str2 = "media_" + appWidgetId + '.' + str;
            File file = new File(getFilesDir(), str2);
            FileOutputStream fileOutputStream = inputStreamOpenInputStream;
            try {
                InputStream inputStream = fileOutputStream;
                fileOutputStream = new FileOutputStream(file);
                try {
                    ByteStreamsKt.copyTo$default(inputStream, fileOutputStream, 0, 2, null);
                    CloseableKt.closeFinally(fileOutputStream, null);
                    CloseableKt.closeFinally(fileOutputStream, null);
                    if (!file.exists()) {
                        Log.e("VideoWidgetConfigure", "Copied file does not exist: " + file.getAbsolutePath());
                        return null;
                    }
                    Log.d("VideoWidgetConfigure", "Copied media path: " + file.getAbsolutePath());
                    WidgetRepository.INSTANCE.logAction("saved", str2);
                    return file.getAbsolutePath();
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            Log.e("VideoWidgetConfigure", "Exception occurred while copying media: " + e.getLocalizedMessage(), e);
            return null;
        }
    }

    private final String copyImageToInternalStorage(Uri uri, int appWidgetId) throws FileNotFoundException {
        try {
            InputStream inputStreamOpenInputStream = getContentResolver().openInputStream(uri);
            if (inputStreamOpenInputStream == null) {
                return null;
            }
            String str = "media_" + appWidgetId + ".png";
            File file = new File(getFilesDir(), str);
            FileOutputStream fileOutputStream = inputStreamOpenInputStream;
            try {
                InputStream inputStream = fileOutputStream;
                fileOutputStream = new FileOutputStream(file);
                try {
                    ByteStreamsKt.copyTo$default(inputStream, fileOutputStream, 0, 2, null);
                    CloseableKt.closeFinally(fileOutputStream, null);
                    CloseableKt.closeFinally(fileOutputStream, null);
                    if (!file.exists()) {
                        Log.e("VideoWidgetConfigure", "Copied image file does not exist: " + file.getAbsolutePath());
                        return null;
                    }
                    Log.d("VideoWidgetConfigure", "Copied image path: " + file.getAbsolutePath());
                    WidgetRepository.INSTANCE.logAction("saved", str);
                    return file.getAbsolutePath();
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            Log.e("VideoWidgetConfigure", "Exception occurred while copying image: " + e.getLocalizedMessage(), e);
            return null;
        }
    }

    private final void checkAndShowFeedbackDialog() {
        Date date;
        Date date2 = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        SharedPreferences sharedPreferences = this.sharedPreferences;
        SharedPreferences sharedPreferences2 = null;
        if (sharedPreferences == null) {
            Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            sharedPreferences = null;
        }
        String string = sharedPreferences.getString(KEY_LAST_FEEDBACK_SHOWN, null);
        if (string != null) {
            try {
                date = simpleDateFormat.parse(string);
            } catch (Exception unused) {
            }
        } else {
            date = null;
        }
        if (date == null || date.before(date2)) {
            Log.d("VideoWidgetConfigure", "Showing feedback dialog.");
            showFeedbackDialog();
            SharedPreferences sharedPreferences3 = this.sharedPreferences;
            if (sharedPreferences3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("sharedPreferences");
            } else {
                sharedPreferences2 = sharedPreferences3;
            }
            sharedPreferences2.edit().putString(KEY_LAST_FEEDBACK_SHOWN, simpleDateFormat.format(date2)).apply();
            return;
        }
        Log.d("VideoWidgetConfigure", "Feedback dialog already shown today.");
    }

    private final void showFeedbackDialog() {
        final ReviewManager reviewManagerCreate = ReviewManagerFactory.create(getApplicationContext());
        Intrinsics.checkNotNullExpressionValue(reviewManagerCreate, "create(...)");
        Task<ReviewInfo> taskRequestReviewFlow = reviewManagerCreate.requestReviewFlow();
        Intrinsics.checkNotNullExpressionValue(taskRequestReviewFlow, "requestReviewFlow(...)");
        taskRequestReviewFlow.addOnCompleteListener(new OnCompleteListener() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.tasks.OnCompleteListener
            public final void onComplete(Task task) {
                VideoWidgetConfigureActivity.showFeedbackDialog$lambda$21(reviewManagerCreate, this, task);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showFeedbackDialog$lambda$21(ReviewManager reviewManager, VideoWidgetConfigureActivity this$0, Task task) {
        Intrinsics.checkNotNullParameter(reviewManager, "$reviewManager");
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        Intrinsics.checkNotNullParameter(task, "task");
        if (task.isSuccessful()) {
            Task<Void> taskLaunchReviewFlow = reviewManager.launchReviewFlow(this$0, (ReviewInfo) task.getResult());
            Intrinsics.checkNotNullExpressionValue(taskLaunchReviewFlow, "launchReviewFlow(...)");
            taskLaunchReviewFlow.addOnCompleteListener(new OnCompleteListener() { // from class: com.proxy.videowidget.presentation.widget.VideoWidgetConfigureActivity$$ExternalSyntheticLambda2
                @Override // com.google.android.gms.tasks.OnCompleteListener
                public final void onComplete(Task task2) {
                    Intrinsics.checkNotNullParameter(task2, "it");
                }
            });
            return;
        }
        Log.e("VideoWidgetConfigure", "Failed to launch review flow.");
    }
}
