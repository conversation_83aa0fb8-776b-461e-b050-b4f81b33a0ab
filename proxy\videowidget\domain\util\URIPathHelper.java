package com.proxy.videowidget.domain.util;

import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.io.ByteStreamsKt;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: URIPathHelper.kt */
@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J9\u0010\t\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\n\u001a\u0004\u0018\u00010\u00042\u000e\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\fH\u0002¢\u0006\u0002\u0010\rJ\u001a\u0010\u000e\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0012\u0010\u000f\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u001a\u0010\u0010\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u001a\u0010\u0011\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u001a\u0010\u0013\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\bH\u0002¨\u0006\u0019"}, d2 = {"Lcom/proxy/videowidget/domain/util/URIPathHelper;", "", "()V", "copyFileToInternalStorage", "", "context", "Landroid/content/Context;", "uri", "Landroid/net/Uri;", "getDataColumn", "selection", "selectionArgs", "", "(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;", "getDownloadsDocumentPath", "getExternalStorageDocumentPath", "getFileName", "getMediaDocumentPath", "getPath", "handleContentUri", "isDownloadsDocument", "", "isExternalStorageDocument", "isGooglePhotosUri", "isMediaDocument", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class URIPathHelper {
    public final String getPath(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        if (DocumentsContract.isDocumentUri(context, uri)) {
            if (isExternalStorageDocument(uri)) {
                return getExternalStorageDocumentPath(uri);
            }
            if (isDownloadsDocument(uri)) {
                return getDownloadsDocumentPath(context, uri);
            }
            if (isMediaDocument(uri)) {
                return getMediaDocumentPath(context, uri);
            }
            return null;
        }
        if (StringsKt.equals("content", uri.getScheme(), true)) {
            return handleContentUri(context, uri);
        }
        if (StringsKt.equals("file", uri.getScheme(), true)) {
            return uri.getPath();
        }
        return null;
    }

    private final String getExternalStorageDocumentPath(Uri uri) {
        String documentId = DocumentsContract.getDocumentId(uri);
        Intrinsics.checkNotNull(documentId);
        List listSplit$default = StringsKt.split$default((CharSequence) documentId, new String[]{":"}, false, 0, 6, (Object) null);
        String str = (String) CollectionsKt.getOrNull(listSplit$default, 0);
        if (str != null && StringsKt.equals("primary", str, true)) {
            return Environment.getExternalStorageDirectory().toString() + '/' + ((String) CollectionsKt.getOrNull(listSplit$default, 1));
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r5v0, types: [com.proxy.videowidget.domain.util.URIPathHelper] */
    /* JADX WARN: Type inference failed for: r6v0, types: [android.content.Context] */
    /* JADX WARN: Type inference failed for: r6v1, types: [android.content.Context] */
    /* JADX WARN: Type inference failed for: r6v4, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r6v7 */
    /* JADX WARN: Type inference failed for: r6v8 */
    private final String getDownloadsDocumentPath(Context context, Uri uri) throws Throwable {
        String documentId = DocumentsContract.getDocumentId(uri);
        Intrinsics.checkNotNull(documentId);
        if (StringsKt.startsWith$default(documentId, "raw:", false, 2, (Object) null)) {
            return StringsKt.removePrefix(documentId, (CharSequence) "raw:");
        }
        try {
            Long longOrNull = StringsKt.toLongOrNull(documentId);
            if (longOrNull != null) {
                Uri uriWithAppendedId = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), longOrNull.longValue());
                Intrinsics.checkNotNullExpressionValue(uriWithAppendedId, "withAppendedId(...)");
                context = getDataColumn(context, uriWithAppendedId, null, null);
            } else {
                context = copyFileToInternalStorage(context, uri);
            }
            return context;
        } catch (NumberFormatException e) {
            Log.e("URIPathHelper", "NumberFormatException: " + e.getMessage());
            return copyFileToInternalStorage(context, uri);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:22:0x0058  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final java.lang.String getMediaDocumentPath(android.content.Context r8, android.net.Uri r9) {
        /*
            r7 = this;
            java.lang.String r9 = android.provider.DocumentsContract.getDocumentId(r9)
            kotlin.jvm.internal.Intrinsics.checkNotNull(r9)
            r0 = r9
            java.lang.CharSequence r0 = (java.lang.CharSequence) r0
            r9 = 1
            java.lang.String[] r1 = new java.lang.String[r9]
            java.lang.String r2 = ":"
            r6 = 0
            r1[r6] = r2
            r4 = 6
            r5 = 0
            r2 = 0
            r3 = 0
            java.util.List r0 = kotlin.text.StringsKt.split$default(r0, r1, r2, r3, r4, r5)
            java.lang.Object r1 = kotlin.collections.CollectionsKt.getOrNull(r0, r6)
            java.lang.String r1 = (java.lang.String) r1
            r2 = 0
            if (r1 != 0) goto L24
            return r2
        L24:
            int r3 = r1.hashCode()
            r4 = 93166550(0x58d9bd6, float:1.3316821E-35)
            if (r3 == r4) goto L50
            r4 = 100313435(0x5faa95b, float:2.3572098E-35)
            if (r3 == r4) goto L44
            r4 = 112202875(0x6b0147b, float:6.6233935E-35)
            if (r3 == r4) goto L38
            goto L58
        L38:
            java.lang.String r3 = "video"
            boolean r1 = r1.equals(r3)
            if (r1 != 0) goto L41
            goto L58
        L41:
            android.net.Uri r1 = android.provider.MediaStore.Video.Media.EXTERNAL_CONTENT_URI
            goto L5c
        L44:
            java.lang.String r3 = "image"
            boolean r1 = r1.equals(r3)
            if (r1 != 0) goto L4d
            goto L58
        L4d:
            android.net.Uri r1 = android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            goto L5c
        L50:
            java.lang.String r3 = "audio"
            boolean r1 = r1.equals(r3)
            if (r1 != 0) goto L5a
        L58:
            r1 = r2
            goto L5c
        L5a:
            android.net.Uri r1 = android.provider.MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
        L5c:
            if (r1 != 0) goto L5f
            return r2
        L5f:
            java.lang.String[] r3 = new java.lang.String[r9]
            java.lang.Object r9 = kotlin.collections.CollectionsKt.getOrNull(r0, r9)
            java.lang.String r9 = (java.lang.String) r9
            if (r9 != 0) goto L6a
            return r2
        L6a:
            r3[r6] = r9
            java.lang.String r9 = "_id=?"
            java.lang.String r8 = r7.getDataColumn(r8, r1, r9, r3)
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.util.URIPathHelper.getMediaDocumentPath(android.content.Context, android.net.Uri):java.lang.String");
    }

    private final String handleContentUri(Context context, Uri uri) {
        return isGooglePhotosUri(uri) ? uri.getLastPathSegment() : getDataColumn(context, uri, null, null);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0031 A[PHI: r10
  0x0031: PHI (r10v3 android.database.Cursor) = (r10v2 android.database.Cursor), (r10v4 android.database.Cursor) binds: [B:20:0x004f, B:13:0x002f] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0057  */
    /* JADX WARN: Type inference failed for: r1v2 */
    /* JADX WARN: Type inference failed for: r1v3, types: [android.database.Cursor] */
    /* JADX WARN: Type inference failed for: r1v4 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final java.lang.String getDataColumn(android.content.Context r10, android.net.Uri r11, java.lang.String r12, java.lang.String[] r13) throws java.lang.Throwable {
        /*
            r9 = this;
            java.lang.String r0 = "getDataColumn: "
            r1 = 1
            java.lang.String[] r4 = new java.lang.String[r1]
            r1 = 0
            java.lang.String r8 = "_data"
            r4[r1] = r8
            r1 = 0
            android.content.ContentResolver r2 = r10.getContentResolver()     // Catch: java.lang.Throwable -> L35 java.lang.Exception -> L37
            r7 = 0
            r3 = r11
            r5 = r12
            r6 = r13
            android.database.Cursor r10 = r2.query(r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L35 java.lang.Exception -> L37
            if (r10 == 0) goto L2f
            boolean r11 = r10.moveToFirst()     // Catch: java.lang.Exception -> L2d java.lang.Throwable -> L53
            if (r11 == 0) goto L2f
            int r11 = r10.getColumnIndexOrThrow(r8)     // Catch: java.lang.Exception -> L2d java.lang.Throwable -> L53
            java.lang.String r11 = r10.getString(r11)     // Catch: java.lang.Exception -> L2d java.lang.Throwable -> L53
            if (r10 == 0) goto L2c
            r10.close()
        L2c:
            return r11
        L2d:
            r11 = move-exception
            goto L39
        L2f:
            if (r10 == 0) goto L52
        L31:
            r10.close()
            goto L52
        L35:
            r11 = move-exception
            goto L55
        L37:
            r11 = move-exception
            r10 = r1
        L39:
            java.lang.String r12 = "URIPathHelper"
            java.lang.StringBuilder r13 = new java.lang.StringBuilder     // Catch: java.lang.Throwable -> L53
            r13.<init>(r0)     // Catch: java.lang.Throwable -> L53
            java.lang.String r11 = r11.getMessage()     // Catch: java.lang.Throwable -> L53
            java.lang.StringBuilder r11 = r13.append(r11)     // Catch: java.lang.Throwable -> L53
            java.lang.String r11 = r11.toString()     // Catch: java.lang.Throwable -> L53
            android.util.Log.e(r12, r11)     // Catch: java.lang.Throwable -> L53
            if (r10 == 0) goto L52
            goto L31
        L52:
            return r1
        L53:
            r11 = move-exception
            r1 = r10
        L55:
            if (r1 == 0) goto L5a
            r1.close()
        L5a:
            throw r11
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.util.URIPathHelper.getDataColumn(android.content.Context, android.net.Uri, java.lang.String, java.lang.String[]):java.lang.String");
    }

    private final String copyFileToInternalStorage(Context context, Uri uri) throws FileNotFoundException {
        try {
            String fileName = getFileName(context, uri);
            if (fileName == null) {
                fileName = "temp_file";
            }
            File file = new File(context.getCacheDir(), fileName);
            InputStream inputStreamOpenInputStream = context.getContentResolver().openInputStream(uri);
            if (inputStreamOpenInputStream != null) {
                FileOutputStream fileOutputStream = inputStreamOpenInputStream;
                try {
                    InputStream inputStream = fileOutputStream;
                    fileOutputStream = new FileOutputStream(file);
                    try {
                        long jCopyTo$default = ByteStreamsKt.copyTo$default(inputStream, fileOutputStream, 0, 2, null);
                        CloseableKt.closeFinally(fileOutputStream, null);
                        Long.valueOf(jCopyTo$default);
                        CloseableKt.closeFinally(fileOutputStream, null);
                    } finally {
                    }
                } finally {
                }
            }
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e("URIPathHelper", "copyFileToInternalStorage: " + e.getMessage());
            return null;
        }
    }

    private final String getFileName(Context context, Uri uri) throws IOException {
        String string;
        Cursor cursorQuery;
        String strSubstring = null;
        if (!Intrinsics.areEqual(uri.getScheme(), "content") || (cursorQuery = context.getContentResolver().query(uri, null, null, null, null)) == null) {
            string = null;
        } else {
            Cursor cursor = cursorQuery;
            try {
                Cursor cursor2 = cursor;
                string = cursor2.moveToFirst() ? cursor2.getString(cursor2.getColumnIndexOrThrow("_display_name")) : null;
                Unit unit = Unit.INSTANCE;
                CloseableKt.closeFinally(cursor, null);
            } catch (Throwable th) {
                try {
                    throw th;
                } catch (Throwable th2) {
                    CloseableKt.closeFinally(cursor, th);
                    throw th2;
                }
            }
        }
        if (string != null) {
            return string;
        }
        String path = uri.getPath();
        Integer numValueOf = path != null ? Integer.valueOf(StringsKt.lastIndexOf$default((CharSequence) path, '/', 0, false, 6, (Object) null)) : null;
        if ((numValueOf != null && numValueOf.intValue() == -1) || numValueOf == null) {
            return path;
        }
        if (path != null) {
            strSubstring = path.substring(numValueOf.intValue() + 1);
            Intrinsics.checkNotNullExpressionValue(strSubstring, "substring(...)");
        }
        return strSubstring;
    }

    private final boolean isExternalStorageDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.externalstorage.documents", uri.getAuthority());
    }

    private final boolean isDownloadsDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.providers.downloads.documents", uri.getAuthority());
    }

    private final boolean isMediaDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.providers.media.documents", uri.getAuthority());
    }

    private final boolean isGooglePhotosUri(Uri uri) {
        return Intrinsics.areEqual("com.google.android.apps.photos.content", uri.getAuthority());
    }
}
