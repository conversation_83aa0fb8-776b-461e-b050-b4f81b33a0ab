package com.proxy.videowidget.domain.util.decoder;

import android.graphics.SurfaceTexture;
import android.opengl.EGL14;
import android.opengl.EGLConfig;
import android.opengl.EGLContext;
import android.opengl.EGLDisplay;
import android.opengl.EGLSurface;
import android.opengl.GLES20;
import android.util.Log;
import android.view.Surface;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.upstream.CmcdConfiguration;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import kotlin.Metadata;
import kotlin.Triple;
import kotlin.Unit;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: CodecOutputSurface.kt */
@Metadata(d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\u0018\u0000 /2\u00020\u0001:\u0001/B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003¢\u0006\u0002\u0010\u0006J\u0006\u0010\u001d\u001a\u00020\u001eJ\u0010\u0010\u001f\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020!H\u0002J\u000e\u0010\"\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020\u0011J\b\u0010$\u001a\u00020\u001eH\u0002J\b\u0010%\u001a\u0004\u0018\u00010\u0018J\b\u0010&\u001a\u00020\u001eH\u0002J\u0012\u0010'\u001a\u00020\u001e2\b\u0010(\u001a\u0004\u0018\u00010\u001aH\u0016J\u0006\u0010)\u001a\u00020\u001eJ\u0016\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020\u00032\u0006\u0010-\u001a\u00020.R\u0016\u0010\u0007\u001a\n \t*\u0004\u0018\u00010\b0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n \t*\u0004\u0018\u00010\u000b0\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u0016\u0010\f\u001a\n \t*\u0004\u0018\u00010\r0\rX\u0082\u000e¢\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n \t*\u0004\u0018\u00010\u000f0\u000fX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0003X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0011X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u000e¢\u0006\u0002\n\u0000¨\u00060"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/CodecOutputSurface;", "Landroid/graphics/SurfaceTexture$OnFrameAvailableListener;", "width", "", "height", "rotationDegrees", "(III)V", "condition", "Ljava/util/concurrent/locks/Condition;", "kotlin.jvm.PlatformType", "eglContext", "Landroid/opengl/EGLContext;", "eglDisplay", "Landroid/opengl/EGLDisplay;", "eglSurface", "Landroid/opengl/EGLSurface;", "frameAvailable", "", "frameSyncLock", "Ljava/util/concurrent/locks/ReentrantLock;", "isStopped", "pixelBuffer", "Ljava/nio/ByteBuffer;", "surface", "Landroid/view/Surface;", "surfaceTexture", "Landroid/graphics/SurfaceTexture;", "textureRender", "Lcom/proxy/videowidget/domain/util/decoder/SurfaceTextureRender;", "awaitNewImage", "", "checkEglError", NotificationCompat.CATEGORY_MESSAGE, "", "drawImage", "invert", "eglSetup", "getSurface", "makeCurrent", "onFrameAvailable", CmcdConfiguration.KEY_STREAM_TYPE, "release", "retrieveFrame", "Lcom/proxy/videowidget/domain/util/decoder/Frame;", "framePos", "timestamp", "", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class CodecOutputSurface implements SurfaceTexture.OnFrameAvailableListener {
    private static final String TAG = "CodecOutputSurface";
    private final Condition condition;
    private boolean frameAvailable;
    private final ReentrantLock frameSyncLock;
    private int height;
    private boolean isStopped;
    private ByteBuffer pixelBuffer;
    private final int rotationDegrees;
    private Surface surface;
    private SurfaceTexture surfaceTexture;
    private SurfaceTextureRender textureRender;
    private int width;
    private EGLDisplay eglDisplay = EGL14.EGL_NO_DISPLAY;
    private EGLContext eglContext = EGL14.EGL_NO_CONTEXT;
    private EGLSurface eglSurface = EGL14.EGL_NO_SURFACE;

    public CodecOutputSurface(int i, int i2, int i3) {
        this.rotationDegrees = i3;
        ReentrantLock reentrantLock = new ReentrantLock();
        this.frameSyncLock = reentrantLock;
        this.condition = reentrantLock.newCondition();
        if (i <= 0 || i2 <= 0) {
            throw new IllegalArgumentException("Invalid dimensions".toString());
        }
        this.width = i;
        this.height = i2;
        eglSetup();
        makeCurrent();
        SurfaceTextureRender surfaceTextureRender = new SurfaceTextureRender();
        this.textureRender = surfaceTextureRender;
        surfaceTextureRender.surfaceCreated();
        StringBuilder sb = new StringBuilder("textureID=");
        SurfaceTextureRender surfaceTextureRender2 = this.textureRender;
        Log.d(TAG, sb.append(surfaceTextureRender2 != null ? Integer.valueOf(surfaceTextureRender2.getTextureID()) : null).toString());
        SurfaceTextureRender surfaceTextureRender3 = this.textureRender;
        Integer numValueOf = surfaceTextureRender3 != null ? Integer.valueOf(surfaceTextureRender3.getTextureID()) : null;
        Intrinsics.checkNotNull(numValueOf);
        SurfaceTexture surfaceTexture = new SurfaceTexture(numValueOf.intValue());
        this.surfaceTexture = surfaceTexture;
        surfaceTexture.setOnFrameAvailableListener(this);
        this.surface = new Surface(this.surfaceTexture);
        ByteBuffer byteBufferAllocateDirect = ByteBuffer.allocateDirect(this.width * this.height * 4);
        Intrinsics.checkNotNullExpressionValue(byteBufferAllocateDirect, "allocateDirect(...)");
        this.pixelBuffer = byteBufferAllocateDirect;
        byteBufferAllocateDirect.order(ByteOrder.LITTLE_ENDIAN);
    }

    private final void eglSetup() {
        EGLDisplay eGLDisplayEglGetDisplay = EGL14.eglGetDisplay(0);
        this.eglDisplay = eGLDisplayEglGetDisplay;
        if (Intrinsics.areEqual(eGLDisplayEglGetDisplay, EGL14.EGL_NO_DISPLAY)) {
            throw new RuntimeException("Unable to get EGL14 display");
        }
        int[] iArr = new int[2];
        if (!EGL14.eglInitialize(this.eglDisplay, iArr, 0, iArr, 1)) {
            throw new RuntimeException("Unable to initialize EGL14");
        }
        EGLConfig[] eGLConfigArr = new EGLConfig[1];
        if (!EGL14.eglChooseConfig(this.eglDisplay, new int[]{12324, 8, 12323, 8, 12322, 8, 12321, 8, 12352, 4, 12339, 1, 12344}, 0, eGLConfigArr, 0, 1, new int[1], 0)) {
            throw new RuntimeException("Unable to find RGB888+recordable ES2 EGL config");
        }
        this.eglContext = EGL14.eglCreateContext(this.eglDisplay, eGLConfigArr[0], EGL14.EGL_NO_CONTEXT, new int[]{12440, 2, 12344}, 0);
        checkEglError("eglCreateContext");
        this.eglSurface = EGL14.eglCreatePbufferSurface(this.eglDisplay, eGLConfigArr[0], new int[]{12375, this.width, 12374, this.height, 12344}, 0);
        checkEglError("eglCreatePBufferSurface");
    }

    public final void release() {
        ReentrantLock reentrantLock = this.frameSyncLock;
        reentrantLock.lock();
        try {
            this.isStopped = true;
            this.condition.signalAll();
            Unit unit = Unit.INSTANCE;
            reentrantLock.unlock();
            if (!Intrinsics.areEqual(this.eglDisplay, EGL14.EGL_NO_DISPLAY)) {
                EGL14.eglMakeCurrent(this.eglDisplay, EGL14.EGL_NO_SURFACE, EGL14.EGL_NO_SURFACE, EGL14.EGL_NO_CONTEXT);
                EGL14.eglDestroySurface(this.eglDisplay, this.eglSurface);
                EGL14.eglDestroyContext(this.eglDisplay, this.eglContext);
                EGL14.eglReleaseThread();
                EGL14.eglTerminate(this.eglDisplay);
            }
            this.eglDisplay = EGL14.EGL_NO_DISPLAY;
            this.eglContext = EGL14.EGL_NO_CONTEXT;
            this.eglSurface = EGL14.EGL_NO_SURFACE;
            Surface surface = this.surface;
            if (surface != null) {
                surface.release();
            }
            this.textureRender = null;
            this.surface = null;
            SurfaceTexture surfaceTexture = this.surfaceTexture;
            if (surfaceTexture != null) {
                surfaceTexture.setOnFrameAvailableListener(null);
            }
            SurfaceTexture surfaceTexture2 = this.surfaceTexture;
            if (surfaceTexture2 != null) {
                surfaceTexture2.release();
            }
            this.surfaceTexture = null;
        } catch (Throwable th) {
            reentrantLock.unlock();
            throw th;
        }
    }

    private final void makeCurrent() {
        EGLDisplay eGLDisplay = this.eglDisplay;
        EGLSurface eGLSurface = this.eglSurface;
        if (!EGL14.eglMakeCurrent(eGLDisplay, eGLSurface, eGLSurface, this.eglContext)) {
            throw new RuntimeException("eglMakeCurrent failed");
        }
    }

    public final Surface getSurface() {
        return this.surface;
    }

    public final void awaitNewImage() {
        ReentrantLock reentrantLock = this.frameSyncLock;
        reentrantLock.lock();
        while (!this.frameAvailable && !this.isStopped) {
            try {
                try {
                    if (!this.condition.await(DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, TimeUnit.MILLISECONDS) && !this.frameAvailable && !this.isStopped) {
                        throw new RuntimeException("Frame wait timed out");
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            } finally {
                reentrantLock.unlock();
            }
        }
        if (this.isStopped) {
            return;
        }
        this.frameAvailable = false;
        Unit unit = Unit.INSTANCE;
        reentrantLock.unlock();
        SurfaceTextureRender surfaceTextureRender = this.textureRender;
        if (surfaceTextureRender != null) {
            surfaceTextureRender.checkGlError("before updateTexImage");
        }
        SurfaceTexture surfaceTexture = this.surfaceTexture;
        Intrinsics.checkNotNull(surfaceTexture);
        surfaceTexture.updateTexImage();
    }

    public final void drawImage(boolean invert) {
        SurfaceTextureRender surfaceTextureRender;
        SurfaceTexture surfaceTexture = this.surfaceTexture;
        if (surfaceTexture == null || (surfaceTextureRender = this.textureRender) == null) {
            return;
        }
        surfaceTextureRender.drawFrame(surfaceTexture, invert);
    }

    @Override // android.graphics.SurfaceTexture.OnFrameAvailableListener
    public void onFrameAvailable(SurfaceTexture st) {
        ReentrantLock reentrantLock = this.frameSyncLock;
        reentrantLock.lock();
        try {
            if (this.isStopped) {
                return;
            }
            if (this.frameAvailable) {
                throw new RuntimeException("mFrameAvailable already set, frame could be dropped");
            }
            this.frameAvailable = true;
            this.condition.signalAll();
            Unit unit = Unit.INSTANCE;
        } finally {
            reentrantLock.unlock();
        }
    }

    public final Frame retrieveFrame(int framePos, long timestamp) throws IOException {
        Triple triple;
        this.pixelBuffer.rewind();
        GLES20.glReadPixels(0, 0, this.width, this.height, 6408, 5121, this.pixelBuffer);
        int i = this.rotationDegrees;
        if (i == 90) {
            triple = new Triple(90, false, true);
        } else if (i == 180) {
            triple = new Triple(180, true, true);
        } else if (i == 270) {
            triple = new Triple(270, true, false);
        } else {
            triple = new Triple(Integer.valueOf(this.rotationDegrees), false, false);
        }
        return new Frame(this.pixelBuffer.duplicate(), this.width, this.height, framePos, timestamp, ((Number) triple.component1()).intValue(), ((Boolean) triple.component2()).booleanValue(), ((Boolean) triple.component3()).booleanValue());
    }

    private final void checkEglError(String msg) {
        int iEglGetError = EGL14.eglGetError();
        if (iEglGetError != 12288) {
            throw new RuntimeException(msg + ": EGL error: 0x" + Integer.toHexString(iEglGetError));
        }
    }
}
