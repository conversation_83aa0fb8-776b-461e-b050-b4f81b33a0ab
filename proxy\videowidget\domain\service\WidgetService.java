package com.proxy.videowidget.domain.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.OutOfQuotaPolicy;
import androidx.work.WorkManager;
import com.proxy.videowidget.R;
import com.proxy.videowidget.domain.worker.WidgetUpdateWorker;
import com.proxy.videowidget.presentation.MainActivity;
import com.proxy.videowidget.presentation.widget.Widget;
import com.proxy.videowidget.repository.WidgetRepository;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.locks.ReentrantLock;
import javax.inject.Inject;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.ResultKt;
import kotlin.TuplesKt;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.coroutines.Continuation;
import kotlin.coroutines.intrinsics.IntrinsicsKt;
import kotlin.coroutines.jvm.internal.Boxing;
import kotlin.coroutines.jvm.internal.DebugMetadata;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.coroutines.BuildersKt__Builders_commonKt;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.CoroutineScopeKt;
import kotlinx.coroutines.DelayKt;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.Job;
import kotlinx.coroutines.SupervisorKt;

/* compiled from: WidgetService.kt */
@Metadata(d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0007\u0018\u0000 +2\u00020\u0001:\u0001+B\u0005¢\u0006\u0002\u0010\u0002J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\rH\u0002J\b\u0010\u0013\u001a\u00020\u0011H\u0002J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0016\u001a\u00020\u0011H\u0002J\u0014\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0016J\b\u0010\u001b\u001a\u00020\u0011H\u0016J\b\u0010\u001c\u001a\u00020\u0011H\u0016J\"\u0010\u001d\u001a\u00020\r2\b\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\u0006\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020\rH\u0016J\b\u0010 \u001a\u00020\u0011H\u0002J\u0010\u0010!\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\rH\u0002J2\u0010\"\u001a\u00020\u00112\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0012\u001a\u00020\r2\u0006\u0010%\u001a\u00020&2\u0006\u0010'\u001a\u00020&2\b\u0010(\u001a\u0004\u0018\u00010&H\u0002J\b\u0010)\u001a\u00020\u0011H\u0002J\b\u0010*\u001a\u00020\u0011H\u0002R\u001e\u0010\u0003\u001a\u00020\u00048\u0006@\u0006X\u0087.¢\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006,"}, d2 = {"Lcom/proxy/videowidget/domain/service/WidgetService;", "Landroid/app/Service;", "()V", "appWidgetManager", "Landroid/appwidget/AppWidgetManager;", "getAppWidgetManager", "()Landroid/appwidget/AppWidgetManager;", "setAppWidgetManager", "(Landroid/appwidget/AppWidgetManager;)V", "deletionLock", "Ljava/util/concurrent/locks/ReentrantLock;", "deletionQueue", "Ljava/util/concurrent/LinkedBlockingQueue;", "", "scope", "Lkotlinx/coroutines/CoroutineScope;", "cancelWidgetWork", "", "appWidgetId", "checkAndStopService", "createNotification", "Landroid/app/Notification;", "logActiveWidgets", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "onStartCommand", "flags", "startId", "processDeletionQueue", "removeInvalidWidget", "startWidgetUpdateWork", "context", "Landroid/content/Context;", "mediaType", "", "inputPath", "framesDirectory", "startWidgetUpdates", "stopWidgetUpdates", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
@AndroidEntryPoint
/* loaded from: classes3.dex */
public final class WidgetService extends Hilt_WidgetService {
    public static final String ACTION_DELETE_WIDGET = "com.proxy.videowidget.ACTION_DELETE_WIDGET";
    public static final String ACTION_START_WIDGET_UPDATES = "com.proxy.videowidget.ACTION_START_WIDGET_UPDATES";
    public static final String ACTION_STOP_WIDGET = "com.proxy.videowidget.ACTION_STOP_WIDGET";
    public static final String ACTION_STOP_WIDGET_UPDATES = "com.proxy.videowidget.ACTION_STOP_WIDGET_UPDATES";
    private static final String CHANNEL_ID = "WidgetServiceChannel";
    private static final int NOTIFICATION_ID = 1;

    @Inject
    public AppWidgetManager appWidgetManager;

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = new Companion(null);
    private static final ConcurrentHashMap.KeySetView<Integer, Boolean> activeWidgets = ConcurrentHashMap.newKeySet();
    private final CoroutineScope scope = CoroutineScopeKt.CoroutineScope(SupervisorKt.SupervisorJob$default((Job) null, 1, (Object) null).plus(Dispatchers.getIO()));
    private final LinkedBlockingQueue<Integer> deletionQueue = new LinkedBlockingQueue<>();
    private final ReentrantLock deletionLock = new ReentrantLock();

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return null;
    }

    public final AppWidgetManager getAppWidgetManager() {
        AppWidgetManager appWidgetManager = this.appWidgetManager;
        if (appWidgetManager != null) {
            return appWidgetManager;
        }
        Intrinsics.throwUninitializedPropertyAccessException("appWidgetManager");
        return null;
    }

    public final void setAppWidgetManager(AppWidgetManager appWidgetManager) {
        Intrinsics.checkNotNullParameter(appWidgetManager, "<set-?>");
        this.appWidgetManager = appWidgetManager;
    }

    /* compiled from: WidgetService.kt */
    @Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\"\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u0010J\u000e\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T¢\u0006\u0002\n\u0000RN\u0010\u000b\u001aB\u0012\f\u0012\n \r*\u0004\u0018\u00010\n0\n\u0012\f\u0012\n \r*\u0004\u0018\u00010\u000e0\u000e \r* \u0012\f\u0012\n \r*\u0004\u0018\u00010\n0\n\u0012\f\u0012\n \r*\u0004\u0018\u00010\u000e0\u000e\u0018\u00010\f0\fX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0013"}, d2 = {"Lcom/proxy/videowidget/domain/service/WidgetService$Companion;", "", "()V", "ACTION_DELETE_WIDGET", "", "ACTION_START_WIDGET_UPDATES", "ACTION_STOP_WIDGET", "ACTION_STOP_WIDGET_UPDATES", "CHANNEL_ID", "NOTIFICATION_ID", "", "activeWidgets", "Ljava/util/concurrent/ConcurrentHashMap$KeySetView;", "kotlin.jvm.PlatformType", "", "getActiveWidgets", "", "isWidgetActive", "appWidgetId", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final Set<Integer> getActiveWidgets() {
            ConcurrentHashMap.KeySetView keySetView = WidgetService.activeWidgets;
            Intrinsics.checkNotNullExpressionValue(keySetView, "access$getActiveWidgets$cp(...)");
            return CollectionsKt.toSet(keySetView);
        }

        public final boolean isWidgetActive(int appWidgetId) {
            return WidgetService.activeWidgets.contains(Integer.valueOf(appWidgetId));
        }
    }

    @Override // com.proxy.videowidget.domain.service.Hilt_WidgetService, android.app.Service
    public void onCreate() {
        super.onCreate();
        processDeletionQueue();
        Log.d("WidgetService", "WidgetService created");
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Failed to restore switch over string. Please report as a decompilation issue */
    /* JADX WARN: Removed duplicated region for block: B:53:0x00a9  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x00ab  */
    @Override // android.app.Service
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public int onStartCommand(android.content.Intent r8, int r9, int r10) {
        /*
            r7 = this;
            int r9 = android.os.Build.VERSION.SDK_INT
            r10 = 26
            r0 = 1
            if (r9 < r10) goto Le
            android.app.Notification r9 = r7.createNotification()
            r7.startForeground(r0, r9)
        Le:
            r9 = -1
            if (r8 == 0) goto L19
            java.lang.String r10 = "appWidgetId"
            int r10 = r8.getIntExtra(r10, r9)
            r3 = r10
            goto L1a
        L19:
            r3 = r9
        L1a:
            r10 = 0
            if (r8 == 0) goto L25
            java.lang.String r1 = "framesDirectory"
            java.lang.String r1 = r8.getStringExtra(r1)
            r6 = r1
            goto L26
        L25:
            r6 = r10
        L26:
            java.lang.String r1 = ""
            if (r8 == 0) goto L32
            java.lang.String r2 = "videoInputPath"
            java.lang.String r2 = r8.getStringExtra(r2)
            if (r2 != 0) goto L33
        L32:
            r2 = r1
        L33:
            if (r8 == 0) goto L3f
            java.lang.String r4 = "gifInputPath"
            java.lang.String r4 = r8.getStringExtra(r4)
            if (r4 != 0) goto L3e
            goto L3f
        L3e:
            r1 = r4
        L3f:
            if (r8 == 0) goto L49
            java.lang.String r4 = "mediaType"
            java.lang.String r4 = r8.getStringExtra(r4)
            if (r4 != 0) goto L4b
        L49:
            java.lang.String r4 = "video"
        L4b:
            if (r8 == 0) goto L51
            java.lang.String r10 = r8.getAction()
        L51:
            if (r10 == 0) goto La9
            int r8 = r10.hashCode()
            r5 = 2
            switch(r8) {
                case 170857899: goto L94;
                case 238032281: goto L87;
                case 621946783: goto L7a;
                case 1674676276: goto L5c;
                default: goto L5b;
            }
        L5b:
            goto La9
        L5c:
            java.lang.String r8 = "com.proxy.videowidget.ACTION_STOP_WIDGET"
            boolean r8 = r10.equals(r8)
            if (r8 != 0) goto L65
            goto La9
        L65:
            if (r3 == r9) goto L79
            java.util.concurrent.ConcurrentHashMap$KeySetView<java.lang.Integer, java.lang.Boolean> r8 = com.proxy.videowidget.domain.service.WidgetService.activeWidgets
            java.lang.Integer r9 = java.lang.Integer.valueOf(r3)
            r8.remove(r9)
            r7.cancelWidgetWork(r3)
            r7.logActiveWidgets()
            r7.checkAndStopService()
        L79:
            return r5
        L7a:
            java.lang.String r8 = "com.proxy.videowidget.ACTION_STOP_WIDGET_UPDATES"
            boolean r8 = r10.equals(r8)
            if (r8 != 0) goto L83
            goto La9
        L83:
            r7.stopWidgetUpdates()
            goto Ld5
        L87:
            java.lang.String r8 = "com.proxy.videowidget.ACTION_START_WIDGET_UPDATES"
            boolean r8 = r10.equals(r8)
            if (r8 != 0) goto L90
            goto La9
        L90:
            r7.startWidgetUpdates()
            goto Ld5
        L94:
            java.lang.String r8 = "com.proxy.videowidget.ACTION_DELETE_WIDGET"
            boolean r8 = r10.equals(r8)
            if (r8 != 0) goto L9d
            goto La9
        L9d:
            if (r3 == r9) goto La8
            java.util.concurrent.LinkedBlockingQueue<java.lang.Integer> r8 = r7.deletionQueue
            java.lang.Integer r9 = java.lang.Integer.valueOf(r3)
            r8.offer(r9)
        La8:
            return r5
        La9:
            if (r3 == r9) goto Ld5
            java.util.concurrent.ConcurrentHashMap$KeySetView<java.lang.Integer, java.lang.Boolean> r8 = com.proxy.videowidget.domain.service.WidgetService.activeWidgets
            java.lang.Integer r9 = java.lang.Integer.valueOf(r3)
            boolean r9 = r8.contains(r9)
            if (r9 != 0) goto Ld5
            java.lang.Integer r9 = java.lang.Integer.valueOf(r3)
            r8.add(r9)
            r7.logActiveWidgets()
            java.lang.CharSequence r2 = (java.lang.CharSequence) r2
            int r8 = r2.length()
            if (r8 != 0) goto Lca
            goto Lcb
        Lca:
            r1 = r2
        Lcb:
            r5 = r1
            java.lang.String r5 = (java.lang.String) r5
            r2 = r7
            android.content.Context r2 = (android.content.Context) r2
            r1 = r7
            r1.startWidgetUpdateWork(r2, r3, r4, r5, r6)
        Ld5:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.service.WidgetService.onStartCommand(android.content.Intent, int, int):int");
    }

    /* compiled from: WidgetService.kt */
    @Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"}, d2 = {"<anonymous>", "", "Lkotlinx/coroutines/CoroutineScope;"}, k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.service.WidgetService$startWidgetUpdates$1", f = "WidgetService.kt", i = {}, l = {}, m = "invokeSuspend", n = {}, s = {})
    /* renamed from: com.proxy.videowidget.domain.service.WidgetService$startWidgetUpdates$1, reason: invalid class name and case insensitive filesystem */
    static final class C01041 extends SuspendLambda implements Function2<CoroutineScope, Continuation<? super Unit>, Object> {
        int label;

        C01041(Continuation<? super C01041> continuation) {
            super(2, continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Continuation<Unit> create(Object obj, Continuation<?> continuation) {
            return WidgetService.this.new C01041(continuation);
        }

        @Override // kotlin.jvm.functions.Function2
        public final Object invoke(CoroutineScope coroutineScope, Continuation<? super Unit> continuation) {
            return ((C01041) create(coroutineScope, continuation)).invokeSuspend(Unit.INSTANCE);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            IntrinsicsKt.getCOROUTINE_SUSPENDED();
            if (this.label != 0) {
                throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
            }
            ResultKt.throwOnFailure(obj);
            List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
            if (value != null) {
                WidgetService widgetService = WidgetService.this;
                for (Widget widget : value) {
                    if (!WidgetService.activeWidgets.contains(Boxing.boxInt(widget.getId()))) {
                        WidgetService.activeWidgets.add(Boxing.boxInt(widget.getId()));
                        String mediaType = widget.getMediaType();
                        if (mediaType == null) {
                            mediaType = "video";
                        }
                        widgetService.startWidgetUpdateWork(widgetService, widget.getId(), mediaType, "", widget.getFramesDirectory());
                    }
                }
            }
            WidgetService.this.logActiveWidgets();
            return Unit.INSTANCE;
        }
    }

    private final void startWidgetUpdates() {
        BuildersKt__Builders_commonKt.launch$default(this.scope, null, null, new C01041(null), 3, null);
    }

    /* compiled from: WidgetService.kt */
    @Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"}, d2 = {"<anonymous>", "", "Lkotlinx/coroutines/CoroutineScope;"}, k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.service.WidgetService$stopWidgetUpdates$1", f = "WidgetService.kt", i = {}, l = {}, m = "invokeSuspend", n = {}, s = {})
    /* renamed from: com.proxy.videowidget.domain.service.WidgetService$stopWidgetUpdates$1, reason: invalid class name and case insensitive filesystem */
    static final class C01051 extends SuspendLambda implements Function2<CoroutineScope, Continuation<? super Unit>, Object> {
        int label;

        C01051(Continuation<? super C01051> continuation) {
            super(2, continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Continuation<Unit> create(Object obj, Continuation<?> continuation) {
            return WidgetService.this.new C01051(continuation);
        }

        @Override // kotlin.jvm.functions.Function2
        public final Object invoke(CoroutineScope coroutineScope, Continuation<? super Unit> continuation) {
            return ((C01051) create(coroutineScope, continuation)).invokeSuspend(Unit.INSTANCE);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            IntrinsicsKt.getCOROUTINE_SUSPENDED();
            if (this.label == 0) {
                ResultKt.throwOnFailure(obj);
                ConcurrentHashMap.KeySetView<Integer> keySetView = WidgetService.activeWidgets;
                Intrinsics.checkNotNullExpressionValue(keySetView, "access$getActiveWidgets$cp(...)");
                WidgetService widgetService = WidgetService.this;
                for (Integer num : keySetView) {
                    Intrinsics.checkNotNull(num);
                    widgetService.cancelWidgetWork(num.intValue());
                }
                WidgetService.activeWidgets.clear();
                WidgetService.this.checkAndStopService();
                WidgetService.this.logActiveWidgets();
                return Unit.INSTANCE;
            }
            throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
        }
    }

    private final void stopWidgetUpdates() {
        BuildersKt__Builders_commonKt.launch$default(this.scope, null, null, new C01051(null), 3, null);
    }

    /* compiled from: WidgetService.kt */
    @Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"}, d2 = {"<anonymous>", "", "Lkotlinx/coroutines/CoroutineScope;"}, k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.service.WidgetService$processDeletionQueue$1", f = "WidgetService.kt", i = {0}, l = {157}, m = "invokeSuspend", n = {"$this$launch"}, s = {"L$0"})
    /* renamed from: com.proxy.videowidget.domain.service.WidgetService$processDeletionQueue$1, reason: invalid class name */
    static final class AnonymousClass1 extends SuspendLambda implements Function2<CoroutineScope, Continuation<? super Unit>, Object> {
        private /* synthetic */ Object L$0;
        int label;

        AnonymousClass1(Continuation<? super AnonymousClass1> continuation) {
            super(2, continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Continuation<Unit> create(Object obj, Continuation<?> continuation) {
            AnonymousClass1 anonymousClass1 = WidgetService.this.new AnonymousClass1(continuation);
            anonymousClass1.L$0 = obj;
            return anonymousClass1;
        }

        @Override // kotlin.jvm.functions.Function2
        public final Object invoke(CoroutineScope coroutineScope, Continuation<? super Unit> continuation) {
            return ((AnonymousClass1) create(coroutineScope, continuation)).invokeSuspend(Unit.INSTANCE);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            CoroutineScope coroutineScope;
            Object coroutine_suspended = IntrinsicsKt.getCOROUTINE_SUSPENDED();
            int i = this.label;
            if (i == 0) {
                ResultKt.throwOnFailure(obj);
                coroutineScope = (CoroutineScope) this.L$0;
            } else {
                if (i != 1) {
                    throw new IllegalStateException("call to 'resume' before 'invoke' with coroutine");
                }
                coroutineScope = (CoroutineScope) this.L$0;
                ResultKt.throwOnFailure(obj);
            }
            while (CoroutineScopeKt.isActive(coroutineScope)) {
                Integer num = (Integer) WidgetService.this.deletionQueue.poll();
                if (num != null) {
                    ReentrantLock reentrantLock = WidgetService.this.deletionLock;
                    WidgetService widgetService = WidgetService.this;
                    reentrantLock.lock();
                    try {
                        widgetService.removeInvalidWidget(num.intValue());
                        Unit unit = Unit.INSTANCE;
                    } finally {
                        reentrantLock.unlock();
                    }
                } else {
                    this.L$0 = coroutineScope;
                    this.label = 1;
                    if (DelayKt.delay(100L, this) == coroutine_suspended) {
                        return coroutine_suspended;
                    }
                }
            }
            return Unit.INSTANCE;
        }
    }

    private final void processDeletionQueue() {
        BuildersKt__Builders_commonKt.launch$default(this.scope, null, null, new AnonymousClass1(null), 3, null);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Multi-variable type inference failed */
    public final void removeInvalidWidget(int appWidgetId) {
        Log.d("WidgetService", "Removing invalid widget: " + appWidgetId);
        cancelWidgetWork(appWidgetId);
        activeWidgets.remove(Integer.valueOf(appWidgetId));
        List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
        Widget widget = null;
        if (value != null) {
            Iterator<T> it = value.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Object next = it.next();
                if (((Widget) next).getId() == appWidgetId) {
                    widget = next;
                    break;
                }
            }
            widget = widget;
        }
        if (widget != null) {
            WidgetRepository.INSTANCE.removeWidget(widget);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void cancelWidgetWork(int appWidgetId) {
        WorkManager.getInstance(this).cancelUniqueWork("WidgetUpdate_" + appWidgetId);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void checkAndStopService() {
        if (activeWidgets.isEmpty()) {
            Log.d("WidgetService", "No active widgets; stopping service.");
            stopForeground(true);
            stopSelf();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void logActiveWidgets() {
        Log.d("WidgetService", "Active widgets: " + activeWidgets);
    }

    @Override // android.app.Service
    public void onDestroy() {
        Log.d("WidgetService", "WidgetService destroyed");
        CoroutineScopeKt.cancel$default(this.scope, null, 1, null);
        super.onDestroy();
    }

    private final Notification createNotification() {
        if (Build.VERSION.SDK_INT >= 26) {
            ((NotificationManager) getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel(CHANNEL_ID, "Widget Service Channel", 2));
        }
        WidgetService widgetService = this;
        Notification notificationBuild = new NotificationCompat.Builder(widgetService, CHANNEL_ID).setContentTitle("Widget Service").setContentText("Processing widget updates").setSmallIcon(R.drawable.ic_logo).setContentIntent(PendingIntent.getActivity(widgetService, 0, new Intent(widgetService, (Class<?>) MainActivity.class), 201326592)).build();
        Intrinsics.checkNotNullExpressionValue(notificationBuild, "build(...)");
        return notificationBuild;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void startWidgetUpdateWork(Context context, int appWidgetId, String mediaType, String inputPath, String framesDirectory) {
        OneTimeWorkRequest.Builder expedited = new OneTimeWorkRequest.Builder(WidgetUpdateWorker.class).setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST);
        Pair[] pairArr = {TuplesKt.to("appWidgetId", Integer.valueOf(appWidgetId)), TuplesKt.to("mediaType", mediaType), TuplesKt.to("inputPath", inputPath), TuplesKt.to("framesDirectory", framesDirectory)};
        Data.Builder builder = new Data.Builder();
        for (int i = 0; i < 4; i++) {
            Pair pair = pairArr[i];
            builder.put((String) pair.getFirst(), pair.getSecond());
        }
        Data dataBuild = builder.build();
        Intrinsics.checkNotNullExpressionValue(dataBuild, "dataBuilder.build()");
        WorkManager.getInstance(context).enqueueUniqueWork("WidgetUpdate_" + appWidgetId, ExistingWorkPolicy.KEEP, expedited.setInputData(dataBuild).build());
    }
}
