package com.proxy.videowidget.domain.worker;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import androidx.work.CoroutineWorker;
import androidx.work.ForegroundInfo;
import androidx.work.ListenableWorker;
import androidx.work.WorkerParameters;
import com.proxy.videowidget.R;
import com.proxy.videowidget.presentation.widget.Widget;
import com.proxy.videowidget.repository.WidgetRepository;
import java.io.File;
import java.util.Iterator;
import java.util.List;
import kotlin.Metadata;
import kotlin.coroutines.Continuation;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlin.coroutines.jvm.internal.DebugMetadata;
import kotlin.io.FilesKt;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: WidgetDeleteWorker.kt */
@Metadata(d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\u0018\u0000 \u00142\u00020\u0001:\u0001\u0014B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005¢\u0006\u0002\u0010\u0006J\b\u0010\u0007\u001a\u00020\bH\u0002J\b\u0010\t\u001a\u00020\nH\u0002J\u000e\u0010\u000b\u001a\u00020\fH\u0096@¢\u0006\u0002\u0010\rJ\u0016\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@¢\u0006\u0002\u0010\u0011J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u0010H\u0002¨\u0006\u0015"}, d2 = {"Lcom/proxy/videowidget/domain/worker/WidgetDeleteWorker;", "Landroidx/work/CoroutineWorker;", "context", "Landroid/content/Context;", "workerParams", "Landroidx/work/WorkerParameters;", "(Landroid/content/Context;Landroidx/work/WorkerParameters;)V", "createForegroundInfo", "Landroidx/work/ForegroundInfo;", "createNotification", "Landroid/app/Notification;", "doWork", "Landroidx/work/ListenableWorker$Result;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleWidgetDeletion", "appWidgetId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeOrphanedWidget", "", "Companion", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WidgetDeleteWorker extends CoroutineWorker {
    public static final String CHANNEL_ID = "WidgetDeleteChannel";
    public static final int NOTIFICATION_ID = 3;

    /* compiled from: WidgetDeleteWorker.kt */
    @Metadata(k = 3, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    @DebugMetadata(c = "com.proxy.videowidget.domain.worker.WidgetDeleteWorker", f = "WidgetDeleteWorker.kt", i = {0, 0}, l = {42, 45}, m = "doWork", n = {"this", "appWidgetId"}, s = {"L$0", "I$0"})
    /* renamed from: com.proxy.videowidget.domain.worker.WidgetDeleteWorker$doWork$1, reason: invalid class name */
    static final class AnonymousClass1 extends ContinuationImpl {
        int I$0;
        Object L$0;
        int label;
        /* synthetic */ Object result;

        AnonymousClass1(Continuation<? super AnonymousClass1> continuation) {
            super(continuation);
        }

        @Override // kotlin.coroutines.jvm.internal.BaseContinuationImpl
        public final Object invokeSuspend(Object obj) {
            this.result = obj;
            this.label |= Integer.MIN_VALUE;
            return WidgetDeleteWorker.this.doWork(this);
        }
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public WidgetDeleteWorker(Context context, WorkerParameters workerParams) {
        super(context, workerParams);
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(workerParams, "workerParams");
    }

    /* JADX WARN: Removed duplicated region for block: B:7:0x0014  */
    @Override // androidx.work.CoroutineWorker
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Object doWork(kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> r8) {
        /*
            r7 = this;
            boolean r0 = r8 instanceof com.proxy.videowidget.domain.worker.WidgetDeleteWorker.AnonymousClass1
            if (r0 == 0) goto L14
            r0 = r8
            com.proxy.videowidget.domain.worker.WidgetDeleteWorker$doWork$1 r0 = (com.proxy.videowidget.domain.worker.WidgetDeleteWorker.AnonymousClass1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r1 = r1 & r2
            if (r1 == 0) goto L14
            int r8 = r0.label
            int r8 = r8 - r2
            r0.label = r8
            goto L19
        L14:
            com.proxy.videowidget.domain.worker.WidgetDeleteWorker$doWork$1 r0 = new com.proxy.videowidget.domain.worker.WidgetDeleteWorker$doWork$1
            r0.<init>(r8)
        L19:
            java.lang.Object r8 = r0.result
            java.lang.Object r1 = kotlin.coroutines.intrinsics.IntrinsicsKt.getCOROUTINE_SUSPENDED()
            int r2 = r0.label
            r3 = 2
            r4 = 1
            if (r2 == 0) goto L3f
            if (r2 == r4) goto L35
            if (r2 != r3) goto L2d
            kotlin.ResultKt.throwOnFailure(r8)
            goto L98
        L2d:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r0)
            throw r8
        L35:
            int r2 = r0.I$0
            java.lang.Object r4 = r0.L$0
            com.proxy.videowidget.domain.worker.WidgetDeleteWorker r4 = (com.proxy.videowidget.domain.worker.WidgetDeleteWorker) r4
            kotlin.ResultKt.throwOnFailure(r8)
            goto L8c
        L3f:
            kotlin.ResultKt.throwOnFailure(r8)
            androidx.work.Data r8 = r7.getInputData()
            java.lang.String r2 = "appWidgetId"
            r5 = -1
            int r2 = r8.getInt(r2, r5)
            if (r2 != r5) goto L60
            java.lang.String r8 = "WidgetDeleteWorker"
            java.lang.String r0 = "Invalid widget ID provided."
            android.util.Log.e(r8, r0)
            androidx.work.ListenableWorker$Result r8 = androidx.work.ListenableWorker.Result.failure()
            java.lang.String r0 = "failure(...)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r8, r0)
            return r8
        L60:
            android.content.Context r8 = r7.getApplicationContext()
            androidx.work.WorkManager r8 = androidx.work.WorkManager.getInstance(r8)
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            java.lang.String r6 = "WidgetUpdate_"
            r5.<init>(r6)
            java.lang.StringBuilder r5 = r5.append(r2)
            java.lang.String r5 = r5.toString()
            r8.cancelUniqueWork(r5)
            androidx.work.ForegroundInfo r8 = r7.createForegroundInfo()
            r0.L$0 = r7
            r0.I$0 = r2
            r0.label = r4
            java.lang.Object r8 = r7.setForeground(r8, r0)
            if (r8 != r1) goto L8b
            return r1
        L8b:
            r4 = r7
        L8c:
            r8 = 0
            r0.L$0 = r8
            r0.label = r3
            java.lang.Object r8 = r4.handleWidgetDeletion(r2, r0)
            if (r8 != r1) goto L98
            return r1
        L98:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: com.proxy.videowidget.domain.worker.WidgetDeleteWorker.doWork(kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Multi-variable type inference failed */
    public final Object handleWidgetDeletion(int i, Continuation<? super ListenableWorker.Result> continuation) {
        List<Widget> value = WidgetRepository.INSTANCE.getWidgets().getValue();
        Widget widget = null;
        if (value != null) {
            Iterator<T> it = value.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Object next = it.next();
                if (((Widget) next).getId() == i) {
                    widget = next;
                    break;
                }
            }
            widget = widget;
        }
        if (widget == null) {
            Log.e("WidgetDeleteWorker", "Widget " + i + " not found. Proceeding with cleanup.");
            removeOrphanedWidget(i);
            ListenableWorker.Result resultSuccess = ListenableWorker.Result.success();
            Intrinsics.checkNotNullExpressionValue(resultSuccess, "success(...)");
            return resultSuccess;
        }
        WidgetRepository.INSTANCE.removeWidget(widget);
        File file = new File(widget.getFramesDirectory());
        if (file.exists() && file.isDirectory()) {
            FilesKt.deleteRecursively(file);
        }
        Log.d("WidgetDeleteWorker", "Widget " + i + " deleted successfully.");
        ListenableWorker.Result resultSuccess2 = ListenableWorker.Result.success();
        Intrinsics.checkNotNullExpressionValue(resultSuccess2, "success(...)");
        return resultSuccess2;
    }

    private final void removeOrphanedWidget(int appWidgetId) {
        File file = new File(getApplicationContext().getFilesDir(), "frames_" + appWidgetId);
        if (file.exists() && file.isDirectory()) {
            FilesKt.deleteRecursively(file);
        }
        Log.d("WidgetDeleteWorker", "Orphaned widget " + appWidgetId + " cleaned up successfully.");
    }

    private final ForegroundInfo createForegroundInfo() {
        Notification notificationCreateNotification = createNotification();
        if (Build.VERSION.SDK_INT >= 29) {
            return new ForegroundInfo(3, notificationCreateNotification, 1);
        }
        return new ForegroundInfo(3, notificationCreateNotification);
    }

    private final Notification createNotification() {
        if (Build.VERSION.SDK_INT >= 26) {
            ((NotificationManager) getApplicationContext().getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel(CHANNEL_ID, "Widget Deletion", 2));
        }
        Notification notificationBuild = new NotificationCompat.Builder(getApplicationContext(), CHANNEL_ID).setContentTitle("Deleting Widget").setSmallIcon(R.drawable.ic_logo).setOngoing(true).build();
        Intrinsics.checkNotNullExpressionValue(notificationBuild, "build(...)");
        return notificationBuild;
    }
}
