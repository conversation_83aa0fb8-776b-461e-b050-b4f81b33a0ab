<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Widget Preview -->
        <ImageView
            android:id="@+id/imageViewPreview"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/widget_background"
            android:scaleType="centerCrop"
            android:contentDescription="Widget preview"
            tools:src="@drawable/placeholder_video" />

        <!-- Widget Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_gravity="center_vertical">

            <TextView
                android:id="@+id/textViewName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Video Widget 1" />

            <TextView
                android:id="@+id/textViewDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Duration: 30s" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- Play/Pause Status -->
                <ImageView
                    android:id="@+id/imageViewStatus"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginEnd="4dp"
                    android:src="@drawable/ic_play"
                    android:contentDescription="Playback status"
                    tools:src="@drawable/ic_pause" />

                <TextView
                    android:id="@+id/textViewStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorTertiary"
                    tools:text="Playing" />

                <!-- Frame Info -->
                <TextView
                    android:id="@+id/textViewFrameInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10sp"
                    android:textColor="?android:attr/textColorTertiary"
                    android:background="@drawable/frame_counter_background"
                    android:padding="4dp"
                    tools:text="15/30" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_vertical">

            <ImageButton
                android:id="@+id/buttonPlayPause"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/play_pause_button_background"
                android:src="@drawable/ic_pause"
                android:contentDescription="@string/play_pause_button_description"
                android:scaleType="centerInside"
                android:padding="8dp" />

            <ImageButton
                android:id="@+id/buttonDelete"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/delete_button_background"
                android:src="@drawable/ic_delete"
                android:contentDescription="@string/action_delete"
                android:scaleType="centerInside"
                android:padding="8dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
