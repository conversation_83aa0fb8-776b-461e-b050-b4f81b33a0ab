package com.proxy.videowidget.domain.util.decoder;

import android.graphics.Bitmap;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import kotlin.Metadata;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import pl.droidsonroids.gif.GifDrawable;

/* compiled from: GifFrameExtractor.kt */
@Metadata(d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nJ \u0010\f\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\nH\u0002J\u0006\u0010\u0011\u001a\u00020\bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0012"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/GifFrameExtractor;", "", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "Lcom/proxy/videowidget/domain/util/decoder/IVideoFrameExtractor;", "(Lcom/proxy/videowidget/domain/util/decoder/IVideoFrameExtractor;)V", "isTerminated", "", "extractFrames", "", "inputFilePath", "", "outputDirPath", "saveFrameAsImage", TypedValues.AttributesType.S_FRAME, "Landroid/graphics/Bitmap;", "frameIndex", "", "terminate", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class GifFrameExtractor {
    private volatile boolean isTerminated;
    private final IVideoFrameExtractor listener;

    public GifFrameExtractor(IVideoFrameExtractor listener) {
        Intrinsics.checkNotNullParameter(listener, "listener");
        this.listener = listener;
    }

    public final void terminate() {
        this.isTerminated = true;
    }

    public final void extractFrames(String inputFilePath, String outputDirPath) throws IOException {
        String outputDirPath2 = outputDirPath;
        Intrinsics.checkNotNullParameter(inputFilePath, "inputFilePath");
        Intrinsics.checkNotNullParameter(outputDirPath2, "outputDirPath");
        if (this.isTerminated) {
            return;
        }
        File file = new File(inputFilePath);
        if (!file.exists()) {
            throw new IOException("GIF file not found at " + inputFilePath);
        }
        GifDrawable gifDrawable = new GifDrawable(file);
        int numberOfFrames = gifDrawable.getNumberOfFrames();
        int duration = gifDrawable.getDuration() / numberOfFrames;
        int i = 0;
        while (i < numberOfFrames && !this.isTerminated) {
            Bitmap bitmapSeekToFrameAndGet = gifDrawable.seekToFrameAndGet(i);
            Intrinsics.checkNotNull(bitmapSeekToFrameAndGet);
            saveFrameAsImage(bitmapSeekToFrameAndGet, i, outputDirPath2);
            this.listener.onCurrentFrameExtracted(new Frame(null, bitmapSeekToFrameAndGet.getWidth(), bitmapSeekToFrameAndGet.getHeight(), i, i * duration, 0, false, false));
            i++;
            outputDirPath2 = outputDirPath;
        }
        this.listener.onAllFrameExtracted(numberOfFrames, gifDrawable.getDuration(), 1000.0f / duration);
        gifDrawable.recycle();
    }

    private final void saveFrameAsImage(Bitmap frame, int frameIndex, String outputDirPath) throws IOException {
        File file = new File(outputDirPath, "frame_" + frameIndex + ".jpeg");
        File parentFile = file.getParentFile();
        if (parentFile != null) {
            parentFile.mkdirs();
        }
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        try {
            frame.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
            CloseableKt.closeFinally(fileOutputStream, null);
            frame.recycle();
        } finally {
        }
    }
}
