package com.proxy.videowidget.presentation.fragments;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;
import com.bumptech.glide.Glide;
import com.proxy.videowidget.R;
import com.proxy.videowidget.presentation.MainActivity;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: WelcomeFragment.kt */
@Metadata(d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005¢\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0016¨\u0006\t"}, d2 = {"Lcom/proxy/videowidget/presentation/fragments/WelcomeFragment;", "Landroidx/fragment/app/Fragment;", "()V", "onViewCreated", "", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final class WelcomeFragment extends Fragment {
    public WelcomeFragment() {
        super(R.layout.fragment_welcome);
    }

    @Override // androidx.fragment.app.Fragment
    public void onViewCreated(View view, Bundle savedInstanceState) {
        Intrinsics.checkNotNullParameter(view, "view");
        super.onViewCreated(view, savedInstanceState);
        View viewFindViewById = view.findViewById(R.id.continue_button);
        Intrinsics.checkNotNullExpressionValue(viewFindViewById, "findViewById(...)");
        ((Button) viewFindViewById).setOnClickListener(new View.OnClickListener() { // from class: com.proxy.videowidget.presentation.fragments.WelcomeFragment$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                WelcomeFragment.onViewCreated$lambda$2(this.f$0, view2);
            }
        });
        Glide.with(this).load(Integer.valueOf(R.drawable.tilted_welcome_phone)).into((ImageView) view.findViewById(R.id.imageView));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void onViewCreated$lambda$2(WelcomeFragment this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        FragmentActivity activity = this$0.getActivity();
        if (activity != null) {
            activity.getSharedPreferences("VideoWidgetPrefs", 0).edit().putBoolean(MainActivity.KEY_INSTRUCTION_COMPLETED, true).apply();
        }
        InstructionFragment instructionFragment = new InstructionFragment();
        FragmentTransaction fragmentTransactionBeginTransaction = this$0.getParentFragmentManager().beginTransaction();
        fragmentTransactionBeginTransaction.replace(R.id.fragment_container, instructionFragment);
        fragmentTransactionBeginTransaction.addToBackStack(null);
        fragmentTransactionBeginTransaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE);
        fragmentTransactionBeginTransaction.commit();
    }
}
