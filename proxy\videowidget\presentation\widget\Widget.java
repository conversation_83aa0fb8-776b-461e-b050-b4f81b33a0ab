package com.proxy.videowidget.presentation.widget;

import android.graphics.Bitmap;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.google.android.gms.measurement.api.AppMeasurementSdk;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: Widget.kt */
@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005¢\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003HÆ\u0003J\t\u0010\u001a\u001a\u00020\u0005HÆ\u0003J\t\u0010\u001b\u001a\u00020\u0005HÆ\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\bHÆ\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0005HÆ\u0003J\t\u0010\u001e\u001a\u00020\u000bHÆ\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0005HÆ\u0003JU\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005HÆ\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010$\u001a\u00020\u0003HÖ\u0001J\t\u0010%\u001a\u00020\u0005HÖ\u0001R\u0011\u0010\u0006\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b¢\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\n\u001a\u00020\u000b¢\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000f¨\u0006&"}, d2 = {"Lcom/proxy/videowidget/presentation/widget/Widget;", "", TtmlNode.ATTR_ID, "", AppMeasurementSdk.ConditionalUserProperty.NAME, "", "description", "firstFrame", "Landroid/graphics/Bitmap;", "framesDirectory", "framerate", "", "mediaType", "(ILjava/lang/String;Ljava/lang/String;Landroid/graphics/Bitmap;Ljava/lang/String;FLjava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getFirstFrame", "()Landroid/graphics/Bitmap;", "getFramerate", "()F", "getFramesDirectory", "getId", "()I", "getMediaType", "getName", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final /* data */ class Widget {
    private final String description;
    private final transient Bitmap firstFrame;
    private final float framerate;
    private final String framesDirectory;
    private final int id;
    private final String mediaType;
    private final String name;

    public static /* synthetic */ Widget copy$default(Widget widget, int i, String str, String str2, Bitmap bitmap, String str3, float f, String str4, int i2, Object obj) {
        if ((i2 & 1) != 0) {
            i = widget.id;
        }
        if ((i2 & 2) != 0) {
            str = widget.name;
        }
        String str5 = str;
        if ((i2 & 4) != 0) {
            str2 = widget.description;
        }
        String str6 = str2;
        if ((i2 & 8) != 0) {
            bitmap = widget.firstFrame;
        }
        Bitmap bitmap2 = bitmap;
        if ((i2 & 16) != 0) {
            str3 = widget.framesDirectory;
        }
        String str7 = str3;
        if ((i2 & 32) != 0) {
            f = widget.framerate;
        }
        float f2 = f;
        if ((i2 & 64) != 0) {
            str4 = widget.mediaType;
        }
        return widget.copy(i, str5, str6, bitmap2, str7, f2, str4);
    }

    /* renamed from: component1, reason: from getter */
    public final int getId() {
        return this.id;
    }

    /* renamed from: component2, reason: from getter */
    public final String getName() {
        return this.name;
    }

    /* renamed from: component3, reason: from getter */
    public final String getDescription() {
        return this.description;
    }

    /* renamed from: component4, reason: from getter */
    public final Bitmap getFirstFrame() {
        return this.firstFrame;
    }

    /* renamed from: component5, reason: from getter */
    public final String getFramesDirectory() {
        return this.framesDirectory;
    }

    /* renamed from: component6, reason: from getter */
    public final float getFramerate() {
        return this.framerate;
    }

    /* renamed from: component7, reason: from getter */
    public final String getMediaType() {
        return this.mediaType;
    }

    public final Widget copy(int id, String name, String description, Bitmap firstFrame, String framesDirectory, float framerate, String mediaType) {
        Intrinsics.checkNotNullParameter(name, "name");
        Intrinsics.checkNotNullParameter(description, "description");
        return new Widget(id, name, description, firstFrame, framesDirectory, framerate, mediaType);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof Widget)) {
            return false;
        }
        Widget widget = (Widget) other;
        return this.id == widget.id && Intrinsics.areEqual(this.name, widget.name) && Intrinsics.areEqual(this.description, widget.description) && Intrinsics.areEqual(this.firstFrame, widget.firstFrame) && Intrinsics.areEqual(this.framesDirectory, widget.framesDirectory) && Float.compare(this.framerate, widget.framerate) == 0 && Intrinsics.areEqual(this.mediaType, widget.mediaType);
    }

    public int hashCode() {
        int iHashCode = ((((Integer.hashCode(this.id) * 31) + this.name.hashCode()) * 31) + this.description.hashCode()) * 31;
        Bitmap bitmap = this.firstFrame;
        int iHashCode2 = (iHashCode + (bitmap == null ? 0 : bitmap.hashCode())) * 31;
        String str = this.framesDirectory;
        int iHashCode3 = (((iHashCode2 + (str == null ? 0 : str.hashCode())) * 31) + Float.hashCode(this.framerate)) * 31;
        String str2 = this.mediaType;
        return iHashCode3 + (str2 != null ? str2.hashCode() : 0);
    }

    public String toString() {
        return "Widget(id=" + this.id + ", name=" + this.name + ", description=" + this.description + ", firstFrame=" + this.firstFrame + ", framesDirectory=" + this.framesDirectory + ", framerate=" + this.framerate + ", mediaType=" + this.mediaType + ')';
    }

    public Widget(int i, String name, String description, Bitmap bitmap, String str, float f, String str2) {
        Intrinsics.checkNotNullParameter(name, "name");
        Intrinsics.checkNotNullParameter(description, "description");
        this.id = i;
        this.name = name;
        this.description = description;
        this.firstFrame = bitmap;
        this.framesDirectory = str;
        this.framerate = f;
        this.mediaType = str2;
    }

    public final int getId() {
        return this.id;
    }

    public final String getName() {
        return this.name;
    }

    public final String getDescription() {
        return this.description;
    }

    public final Bitmap getFirstFrame() {
        return this.firstFrame;
    }

    public final String getFramesDirectory() {
        return this.framesDirectory;
    }

    public final float getFramerate() {
        return this.framerate;
    }

    public /* synthetic */ Widget(int i, String str, String str2, Bitmap bitmap, String str3, float f, String str4, int i2, DefaultConstructorMarker defaultConstructorMarker) {
        this(i, str, str2, bitmap, (i2 & 16) != 0 ? null : str3, (i2 & 32) != 0 ? 30.0f : f, (i2 & 64) != 0 ? "video" : str4);
    }

    public final String getMediaType() {
        return this.mediaType;
    }
}
