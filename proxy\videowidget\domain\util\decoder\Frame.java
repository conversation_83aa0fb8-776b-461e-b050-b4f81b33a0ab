package com.proxy.videowidget.domain.util.decoder;

import androidx.constraintlayout.motion.widget.Key;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.nio.ByteBuffer;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: Frame.kt */
@Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0019\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BG\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0005\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f¢\u0006\u0002\u0010\u000eJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003HÆ\u0003J\t\u0010\u001a\u001a\u00020\u0005HÆ\u0003J\t\u0010\u001b\u001a\u00020\u0005HÆ\u0003J\t\u0010\u001c\u001a\u00020\u0005HÆ\u0003J\t\u0010\u001d\u001a\u00020\tHÆ\u0003J\t\u0010\u001e\u001a\u00020\u0005HÆ\u0003J\t\u0010\u001f\u001a\u00020\fHÆ\u0003J\t\u0010 \u001a\u00020\fHÆ\u0003J[\u0010!\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u00052\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\fHÆ\u0001J\u0013\u0010\"\u001a\u00020\f2\b\u0010#\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010$\u001a\u00020\u0005HÖ\u0001J\t\u0010%\u001a\u00020&HÖ\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003¢\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\f¢\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0013R\u0011\u0010\r\u001a\u00020\f¢\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\n\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0011\u0010\b\u001a\u00020\t¢\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0005¢\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012¨\u0006'"}, d2 = {"Lcom/proxy/videowidget/domain/util/decoder/Frame;", "", "byteBuffer", "Ljava/nio/ByteBuffer;", "width", "", "height", "position", "timestamp", "", Key.ROTATION, "isFlipX", "", "isFlipY", "(Ljava/nio/ByteBuffer;IIIJIZZ)V", "getByteBuffer", "()Ljava/nio/ByteBuffer;", "getHeight", "()I", "()Z", "getPosition", "getRotation", "getTimestamp", "()J", "getWidth", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "", "app_release"}, k = 1, mv = {1, 9, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes3.dex */
public final /* data */ class Frame {
    private final ByteBuffer byteBuffer;
    private final int height;
    private final boolean isFlipX;
    private final boolean isFlipY;
    private final int position;
    private final int rotation;
    private final long timestamp;
    private final int width;

    /* renamed from: component1, reason: from getter */
    public final ByteBuffer getByteBuffer() {
        return this.byteBuffer;
    }

    /* renamed from: component2, reason: from getter */
    public final int getWidth() {
        return this.width;
    }

    /* renamed from: component3, reason: from getter */
    public final int getHeight() {
        return this.height;
    }

    /* renamed from: component4, reason: from getter */
    public final int getPosition() {
        return this.position;
    }

    /* renamed from: component5, reason: from getter */
    public final long getTimestamp() {
        return this.timestamp;
    }

    /* renamed from: component6, reason: from getter */
    public final int getRotation() {
        return this.rotation;
    }

    /* renamed from: component7, reason: from getter */
    public final boolean getIsFlipX() {
        return this.isFlipX;
    }

    /* renamed from: component8, reason: from getter */
    public final boolean getIsFlipY() {
        return this.isFlipY;
    }

    public final Frame copy(ByteBuffer byteBuffer, int width, int height, int position, long timestamp, int rotation, boolean isFlipX, boolean isFlipY) {
        return new Frame(byteBuffer, width, height, position, timestamp, rotation, isFlipX, isFlipY);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof Frame)) {
            return false;
        }
        Frame frame = (Frame) other;
        return Intrinsics.areEqual(this.byteBuffer, frame.byteBuffer) && this.width == frame.width && this.height == frame.height && this.position == frame.position && this.timestamp == frame.timestamp && this.rotation == frame.rotation && this.isFlipX == frame.isFlipX && this.isFlipY == frame.isFlipY;
    }

    public int hashCode() {
        ByteBuffer byteBuffer = this.byteBuffer;
        return ((((((((((((((byteBuffer == null ? 0 : byteBuffer.hashCode()) * 31) + Integer.hashCode(this.width)) * 31) + Integer.hashCode(this.height)) * 31) + Integer.hashCode(this.position)) * 31) + Long.hashCode(this.timestamp)) * 31) + Integer.hashCode(this.rotation)) * 31) + Boolean.hashCode(this.isFlipX)) * 31) + Boolean.hashCode(this.isFlipY);
    }

    public String toString() {
        return "Frame(byteBuffer=" + this.byteBuffer + ", width=" + this.width + ", height=" + this.height + ", position=" + this.position + ", timestamp=" + this.timestamp + ", rotation=" + this.rotation + ", isFlipX=" + this.isFlipX + ", isFlipY=" + this.isFlipY + ')';
    }

    public Frame(ByteBuffer byteBuffer, int i, int i2, int i3, long j, int i4, boolean z, boolean z2) {
        this.byteBuffer = byteBuffer;
        this.width = i;
        this.height = i2;
        this.position = i3;
        this.timestamp = j;
        this.rotation = i4;
        this.isFlipX = z;
        this.isFlipY = z2;
    }

    public final ByteBuffer getByteBuffer() {
        return this.byteBuffer;
    }

    public final int getWidth() {
        return this.width;
    }

    public final int getHeight() {
        return this.height;
    }

    public final int getPosition() {
        return this.position;
    }

    public final long getTimestamp() {
        return this.timestamp;
    }

    public final int getRotation() {
        return this.rotation;
    }

    public final boolean isFlipX() {
        return this.isFlipX;
    }

    public final boolean isFlipY() {
        return this.isFlipY;
    }
}
