package com.proxy.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.proxy.videowidget.R;

/* loaded from: classes3.dex */
public final class FragmentSavedWidgetsBinding implements ViewBinding {
    public final FloatingActionButton addFab;
    public final TextView emptyListText;
    public final RecyclerView recyclerViewPresets;
    private final ConstraintLayout rootView;

    private FragmentSavedWidgetsBinding(ConstraintLayout constraintLayout, FloatingActionButton floatingActionButton, TextView textView, RecyclerView recyclerView) {
        this.rootView = constraintLayout;
        this.addFab = floatingActionButton;
        this.emptyListText = textView;
        this.recyclerViewPresets = recyclerView;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static FragmentSavedWidgetsBinding inflate(LayoutInflater layoutInflater) {
        return inflate(layoutInflater, null, false);
    }

    public static FragmentSavedWidgetsBinding inflate(LayoutInflater layoutInflater, ViewGroup viewGroup, boolean z) {
        View viewInflate = layoutInflater.inflate(R.layout.fragment_saved_widgets, viewGroup, false);
        if (z) {
            viewGroup.addView(viewInflate);
        }
        return bind(viewInflate);
    }

    public static FragmentSavedWidgetsBinding bind(View view) {
        int i = R.id.addFab;
        FloatingActionButton floatingActionButton = (FloatingActionButton) ViewBindings.findChildViewById(view, i);
        if (floatingActionButton != null) {
            i = R.id.emptyListText;
            TextView textView = (TextView) ViewBindings.findChildViewById(view, i);
            if (textView != null) {
                i = R.id.recyclerViewPresets;
                RecyclerView recyclerView = (RecyclerView) ViewBindings.findChildViewById(view, i);
                if (recyclerView != null) {
                    return new FragmentSavedWidgetsBinding((ConstraintLayout) view, floatingActionButton, textView, recyclerView);
                }
            }
        }
        throw new NullPointerException("Missing required view with ID: ".concat(view.getResources().getResourceName(i)));
    }
}
